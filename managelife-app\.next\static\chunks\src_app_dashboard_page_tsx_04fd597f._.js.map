{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport {\n  Building2,\n  Home,\n  Search,\n  Users,\n  Calendar,\n  TrendingUp,\n  Coins,\n  FileText,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  AlertCircle,\n  CheckCircle,\n  Gift,\n  Bell,\n  Briefcase,\n  UserCircle,\n  CreditCard,\n  Wrench,\n  Heart,\n  HandCoins,\n  ShoppingCart,\n  FileBarChart,\n  UserPlus,\n  BarChart3\n} from 'lucide-react';\nimport { UserRole } from '@/types';\nimport { DASHBOARD_NAV_ITEMS, getRoleDisplayName, getRoleColor } from '@/constants';\nimport { useAuth } from '@/contexts/AuthContext';\nimport RewardCenter from '@/components/rewards/RewardCenter';\nimport Leaderboard from '@/components/rewards/Leaderboard';\nimport SettingsPanel from '@/components/dashboard/SettingsPanel';\nimport PortfolioPanel from '@/components/dashboard/PortfolioPanel';\nimport RentalsPanel from '@/components/dashboard/RentalsPanel';\nimport ProfilePanel from '@/components/dashboard/ProfilePanel';\nimport NotificationsPanel from '@/components/dashboard/NotificationsPanel';\nimport PropertiesPanel from '@/components/dashboard/PropertiesPanel';\nimport NFTsPanel from '@/components/dashboard/NFTsPanel';\nimport PaymentsPanel from '@/components/dashboard/PaymentsPanel';\nimport MaintenancePanel from '@/components/dashboard/MaintenancePanel';\nimport MarketplacePanel from '@/components/dashboard/MarketplacePanel';\nimport FavoritesPanel from '@/components/dashboard/FavoritesPanel';\nimport OffersPanel from '@/components/dashboard/OffersPanel';\nimport TenantsPanel from '@/components/dashboard/TenantsPanel';\nimport ReportsPanel from '@/components/dashboard/ReportsPanel';\nimport CommunityPanel from '@/components/dashboard/CommunityPanel';\nimport EventsPanel from '@/components/dashboard/EventsPanel';\nimport ReferralsPanel from '@/components/dashboard/ReferralsPanel';\nimport NFTRentalPanel from '@/components/dashboard/NFTRentalPanel';\nimport HomeownerDashboard from '@/components/dashboard/homeowner/HomeownerDashboard';\nimport RenterDashboard from '@/components/dashboard/renter/RenterDashboard';\nimport BuyerDashboard from '@/components/dashboard/buyer/BuyerDashboard';\nimport PortfolioManagerDashboard from '@/components/dashboard/portfolio-manager/PortfolioManagerDashboard';\nimport CommunityMemberDashboard from '@/components/dashboard/community-member/CommunityMemberDashboard';\n\nexport default function DashboardPage() {\n  const { user, loading, logout } = useAuth();\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const isWelcome = searchParams.get('welcome') === 'true';\n\n  const [activeRole, setActiveRole] = useState<UserRole>('buyer');\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [showWelcome, setShowWelcome] = useState(isWelcome);\n  const [activeTab, setActiveTab] = useState<'overview' | 'rewards' | 'leaderboard' | 'portfolio' | 'rentals' | 'nft-rental' | 'settings' | 'profile' | 'properties' | 'nfts' | 'notifications' | 'analytics' | 'payments' | 'maintenance' | 'marketplace' | 'favorites' | 'offers' | 'purchases' | 'tenants' | 'reports' | 'community' | 'events' | 'referrals'>('overview');\n\n  // Handle URL tab parameter\n  useEffect(() => {\n    const tabParam = searchParams.get('tab');\n    const validTabs = ['overview', 'rewards', 'leaderboard', 'portfolio', 'rentals', 'nft-rental', 'settings', 'profile', 'properties', 'nfts', 'notifications', 'analytics', 'payments', 'maintenance', 'marketplace', 'favorites', 'offers', 'purchases', 'tenants', 'reports', 'community', 'events', 'referrals'];\n    if (tabParam && validTabs.includes(tabParam)) {\n      setActiveTab(tabParam as any);\n    }\n  }, [searchParams]);\n\n  // Redirect to login if not authenticated\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/auth/login');\n    }\n  }, [user, loading, router]);\n\n  // Set initial active role when user data loads\n  useEffect(() => {\n    if (user && user.roles.length > 0) {\n      setActiveRole(user.roles[0]);\n    }\n  }, [user]);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading your dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error if no user\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  const navItems = DASHBOARD_NAV_ITEMS[activeRole] || [];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 lg:flex mobile-safe-area\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`fixed inset-y-0 left-0 z-50 w-72 sm:w-80 lg:w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:z-auto lg:flex-shrink-0 ${\n        sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n      }`}>\n        <div className=\"flex items-center justify-between h-14 sm:h-16 px-4 sm:px-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-2\">\n            <img\n              src=\"/logo/logo.png\"\n              alt=\"ManageLife\"\n              className=\"h-8 w-auto\"\n            />\n          </div>\n          <button\n            onClick={() => setSidebarOpen(false)}\n            className=\"touch-target lg:hidden text-gray-500 hover:text-gray-700 p-2 rounded-md\"\n          >\n            <X className=\"w-6 h-6\" />\n          </button>\n        </div>\n\n        {/* User Info */}\n        <div className=\"p-4 sm:p-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-3 mb-4\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\n              <span className=\"text-white font-semibold text-sm\">\n                {user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}\n              </span>\n            </div>\n            <div className=\"min-w-0 flex-1\">\n              <h3 className=\"font-semibold text-gray-900 truncate\">{user.name || 'User'}</h3>\n              <p className=\"text-sm text-gray-600 truncate mobile-text-responsive\">{user.email || user.walletAddress}</p>\n              {user.walletAddress && (\n                <p className=\"text-xs text-gray-500 font-mono\">\n                  {user.walletAddress.slice(0, 6)}...{user.walletAddress.slice(-4)}\n                </p>\n              )}\n            </div>\n          </div>\n\n          {/* Role Selector */}\n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Active Role</label>\n            <select\n              value={activeRole}\n              onChange={(e) => setActiveRole(e.target.value as UserRole)}\n              className=\"mobile-input w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            >\n              {user.roles.map((role) => (\n                <option key={role} value={role}>\n                  {getRoleDisplayName(role)}\n                </option>\n              ))}\n            </select>\n          </div>\n\n          {/* $MLIFE Balance */}\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <span className=\"text-sm font-medium text-gray-700 mobile-text-responsive\">$MLIFE Balance</span>\n              <Coins className=\"w-4 h-4 text-blue-600\" />\n            </div>\n            <p className=\"text-lg font-bold text-blue-600\">{user.mlifeBalance.toLocaleString()}</p>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 px-6 py-4\">\n          <ul className=\"space-y-2\">\n            {navItems.map((item) => {\n              const IconComponent = {\n                Home,\n                Building: Building2,\n                Building2,\n                Coins,\n                FileText,\n                Search,\n                Users,\n                Calendar,\n                TrendingUp,\n                Bell,\n                Gift,\n                CreditCard,\n                Wrench,\n                Heart,\n                HandCoins,\n                ShoppingCart,\n                FileBarChart,\n                UserPlus,\n                BarChart3,\n              }[item.icon as keyof typeof import('lucide-react')] || Home;\n\n              const handleNavClick = (e: React.MouseEvent) => {\n                e.preventDefault();\n                if (item.tab) {\n                  setActiveTab(item.tab as any);\n                  // Update URL without page reload\n                  const newUrl = item.tab === 'overview' ? '/dashboard' : `/dashboard?tab=${item.tab}`;\n                  window.history.pushState({}, '', newUrl);\n                }\n              };\n\n              return (\n                <li key={item.href}>\n                  <button\n                    onClick={handleNavClick}\n                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-left ${\n                      (item.tab === 'overview' && activeTab === 'overview') || activeTab === item.tab\n                        ? 'bg-blue-50 text-blue-600'\n                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'\n                    }`}\n                  >\n                    <IconComponent className=\"w-5 h-5\" />\n                    <span>{item.label}</span>\n                  </button>\n                </li>\n              );\n            })}\n          </ul>\n        </nav>\n\n        {/* Bottom Actions */}\n        <div className=\"p-4 sm:p-6 border-t border-gray-200\">\n          <div className=\"space-y-2\">\n            <Link\n              href=\"/settings\"\n              className=\"mobile-nav-item flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <Settings className=\"w-5 h-5\" />\n              <span>Settings</span>\n            </Link>\n            <button\n              onClick={handleLogout}\n              className=\"mobile-nav-item flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors w-full text-left\"\n            >\n              <LogOut className=\"w-5 h-5\" />\n              <span>Sign Out</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 lg:flex lg:flex-col lg:min-w-0\">\n        {/* Top Bar */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"mobile-container flex items-center justify-between h-14 sm:h-16\">\n            <div className=\"flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1\">\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"touch-target lg:hidden text-gray-500 hover:text-gray-700 p-2 rounded-md\"\n              >\n                <Menu className=\"w-6 h-6\" />\n              </button>\n              <h1 className=\"text-lg sm:text-xl font-semibold text-gray-900 truncate\">\n                {getRoleDisplayName(activeRole)} Dashboard\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n              <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${getRoleColor(activeRole)}`}>\n                {getRoleDisplayName(activeRole)}\n              </span>\n            </div>\n          </div>\n        </header>\n\n        {/* Dashboard Content */}\n        <main className=\"mobile-container py-4 sm:py-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            {/* Tab Navigation */}\n            <div className=\"mb-6 sm:mb-8\">\n              <div className=\"mobile-nav-scroll-container w-full overflow-x-auto\">\n                <div className=\"mobile-dashboard-nav min-w-max mx-auto\" style={{width: 'fit-content'}}>\n                  <button\n                    onClick={() => setActiveTab('overview')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'overview' ? 'active' : ''\n                    }`}\n                  >\n                    <Home className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Overview</span>\n                    <span className=\"sm:hidden\">Home</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('rewards')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'rewards' ? 'active' : ''\n                    }`}\n                  >\n                    <Gift className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Rewards</span>\n                    <span className=\"sm:hidden\">Coins</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('leaderboard')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'leaderboard' ? 'active' : ''\n                    }`}\n                  >\n                    <TrendingUp className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Leaderboard</span>\n                    <span className=\"sm:hidden\">Rank</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('portfolio')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'portfolio' ? 'active' : ''\n                    }`}\n                  >\n                    <Briefcase className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Portfolio</span>\n                    <span className=\"sm:hidden\">Assets</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('rentals')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'rentals' ? 'active' : ''\n                    }`}\n                  >\n                    <Home className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Rentals</span>\n                    <span className=\"sm:hidden\">Rent</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('profile')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'profile' ? 'active' : ''\n                    }`}\n                  >\n                    <UserCircle className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Profile</span>\n                    <span className=\"sm:hidden\">User</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveTab('settings')}\n                    className={`mobile-dashboard-nav-item ${\n                      activeTab === 'settings' ? 'active' : ''\n                    }`}\n                  >\n                    <Settings className=\"w-4 h-4 mr-1 sm:mr-2 flex-shrink-0\" />\n                    <span className=\"hidden sm:inline\">Settings</span>\n                    <span className=\"sm:hidden\">Config</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n            {/* Tab Content */}\n            {activeTab === 'overview' && (\n              <>\n                {/* Welcome Message for New Users */}\n                {showWelcome && (\n                  <div className=\"mobile-card p-4 sm:p-6 mb-4 sm:mb-6 bg-green-50 border border-green-200\">\n                    <div className=\"flex items-start\">\n                      <CheckCircle className=\"w-5 h-5 sm:w-6 sm:h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5\" />\n                      <div className=\"flex-1 min-w-0\">\n                        <h3 className=\"text-base sm:text-lg font-semibold text-green-900 mb-2\">\n                          Welcome to ManageLife! 🎉\n                        </h3>\n                        <p className=\"text-green-700 mb-3 sm:mb-4 mobile-text-responsive\">\n                          Your account has been created successfully. You've received 1,000 $MLIFE tokens as a welcome bonus!\n                        </p>\n                        <button\n                          onClick={() => setShowWelcome(false)}\n                          className=\"touch-target text-green-600 hover:text-green-700 font-medium text-sm px-2 py-1 rounded-md\"\n                        >\n                          Dismiss\n                        </button>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                {/* Welcome Section */}\n                <div className=\"mobile-card bg-gradient-to-r from-blue-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-white mb-6 sm:mb-8\">\n                  <h2 className=\"text-xl sm:text-2xl font-bold mb-2\">\n                    Welcome back, {user.name || 'User'}!\n                  </h2>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4\">\n                    <div className=\"bg-white/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm border border-white/10 hover:bg-white/25 transition-all duration-200\">\n                      <h3 className=\"font-semibold mb-1 text-sm sm:text-base text-white\">$MLIFE Balance</h3>\n                      <p className=\"text-lg sm:text-2xl font-bold text-white\">{user.mlifeBalance.toLocaleString()}</p>\n                    </div>\n                    <div className=\"bg-white/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm border border-white/10 hover:bg-white/25 transition-all duration-200\">\n                      <h3 className=\"font-semibold mb-1 text-sm sm:text-base text-white\">Active Roles</h3>\n                      <p className=\"text-lg sm:text-2xl font-bold text-white\">{user.roles.length}</p>\n                    </div>\n                    <div className=\"bg-white/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm border border-white/10 hover:bg-white/25 transition-all duration-200\">\n                      <h3 className=\"font-semibold mb-1 text-sm sm:text-base text-white\">Member Since</h3>\n                      <p className=\"text-lg sm:text-2xl font-bold text-white\">{new Date(user.joinedAt).getFullYear()}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Quick Actions */}\n                <div className=\"mobile-grid mb-6 sm:mb-8\">\n                  {navItems.slice(1, 5).map((item) => {\n                    const IconComponent = {\n                      Home,\n                      Building: Building2,\n                      Building2,\n                      Coins,\n                      FileText,\n                      Search,\n                      Users,\n                      Calendar,\n                      TrendingUp,\n                      Bell,\n                    }[item.icon as keyof typeof import('lucide-react')] || Home;\n\n                    return (\n                      <Link\n                        key={item.href}\n                        href={item.href}\n                        className=\"mobile-card p-4 sm:p-6 hover:shadow-md transition-shadow card-hover\"\n                      >\n                        <div className=\"flex items-center space-x-3 sm:space-x-4\">\n                          <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0\">\n                            <IconComponent className=\"w-5 h-5 sm:w-6 sm:h-6 text-blue-600\" />\n                          </div>\n                          <div className=\"min-w-0 flex-1\">\n                            <h3 className=\"font-semibold text-gray-900 text-sm sm:text-base truncate\">{item.label}</h3>\n                            <p className=\"text-xs sm:text-sm text-gray-600\">Quick access</p>\n                          </div>\n                        </div>\n                      </Link>\n                    );\n                  })}\n                </div>\n\n                {/* Role-specific dashboard */}\n                {activeRole === 'homeowner' && <HomeownerDashboard />}\n                {activeRole === 'renter' && <RenterDashboard />}\n                {activeRole === 'buyer' && <BuyerDashboard />}\n                {activeRole === 'portfolio-manager' && <PortfolioManagerDashboard />}\n                {activeRole === 'community-member' && <CommunityMemberDashboard />}\n              </>\n            )}\n\n            {/* Rewards Tab */}\n            {activeTab === 'rewards' && <RewardCenter />}\n\n            {/* Leaderboard Tab */}\n            {activeTab === 'leaderboard' && <Leaderboard />}\n\n            {/* Portfolio Tab */}\n            {activeTab === 'portfolio' && <PortfolioPanel />}\n\n            {/* Rentals Tab */}\n            {activeTab === 'rentals' && <RentalsPanel />}\n\n            {/* Profile Tab */}\n            {activeTab === 'profile' && <ProfilePanel />}\n\n            {/* Settings Tab */}\n            {activeTab === 'settings' && <SettingsPanel />}\n\n            {/* Additional Tabs */}\n            {activeTab === 'properties' && <PropertiesPanel />}\n\n            {activeTab === 'nfts' && <NFTsPanel />}\n\n            {activeTab === 'notifications' && <NotificationsPanel />}\n\n            {activeTab === 'analytics' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Analytics</h2>\n                <p className=\"text-gray-600\">Advanced analytics dashboard coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'payments' && <PaymentsPanel />}\n\n            {activeTab === 'nft-rental' && <NFTRentalPanel />}\n\n            {activeTab === 'maintenance' && <MaintenancePanel />}\n\n            {activeTab === 'marketplace' && <MarketplacePanel />}\n\n            {activeTab === 'favorites' && <FavoritesPanel />}\n\n            {activeTab === 'offers' && <OffersPanel />}\n\n            {activeTab === 'purchases' && (\n              <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Purchases</h2>\n                <p className=\"text-gray-600\">Purchase history and management coming soon.</p>\n              </div>\n            )}\n\n            {activeTab === 'tenants' && <TenantsPanel />}\n\n            {activeTab === 'reports' && <ReportsPanel />}\n\n            {activeTab === 'community' && <CommunityPanel />}\n\n            {activeTab === 'events' && <EventsPanel />}\n\n            {activeTab === 'referrals' && <ReferralsPanel />}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA6BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA5DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8De,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC,eAAe;IAElD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuT;IAEhW,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,WAAW,aAAa,GAAG,CAAC;YAClC,MAAM,YAAY;gBAAC;gBAAY;gBAAW;gBAAe;gBAAa;gBAAW;gBAAc;gBAAY;gBAAW;gBAAc;gBAAQ;gBAAiB;gBAAa;gBAAY;gBAAe;gBAAe;gBAAa;gBAAU;gBAAa;gBAAW;gBAAW;gBAAa;gBAAU;aAAY;YACjT,IAAI,YAAY,UAAU,QAAQ,CAAC,WAAW;gBAC5C,aAAa;YACf;QACF;kCAAG;QAAC;KAAa;IAEjB,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACjC,cAAc,KAAK,KAAK,CAAC,EAAE;YAC7B;QACF;kCAAG;QAAC;KAAK;IAET,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,wBAAwB;IACxB,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,MAAM,WAAW,4HAAA,CAAA,sBAAmB,CAAC,WAAW,IAAI,EAAE;IAEtD,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,AAAC,uLAEhB,OADC,cAAc,kBAAkB;;kCAEhC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDACb,KAAK,IAAI,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM;;;;;;;;;;;kDAGhE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC,KAAK,IAAI,IAAI;;;;;;0DACnE,6LAAC;gDAAE,WAAU;0DAAyD,KAAK,KAAK,IAAI,KAAK,aAAa;;;;;;4CACrG,KAAK,aAAa,kBACjB,6LAAC;gDAAE,WAAU;;oDACV,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG;oDAAG;oDAAI,KAAK,aAAa,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;;;;;;0CAOtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;kDAET,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,qBACf,6LAAC;gDAAkB,OAAO;0DACvB,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;+CADT;;;;;;;;;;;;;;;;0CAQnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2D;;;;;;0DAC3E,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,6LAAC;wCAAE,WAAU;kDAAmC,KAAK,YAAY,CAAC,cAAc;;;;;;;;;;;;;;;;;;kCAKpF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCACX,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,gBAAgB;oCACpB,MAAA,sMAAA,CAAA,OAAI;oCACJ,UAAU,mNAAA,CAAA,YAAS;oCACnB,WAAA,mNAAA,CAAA,YAAS;oCACT,OAAA,uMAAA,CAAA,QAAK;oCACL,UAAA,iNAAA,CAAA,WAAQ;oCACR,QAAA,yMAAA,CAAA,SAAM;oCACN,OAAA,uMAAA,CAAA,QAAK;oCACL,UAAA,6MAAA,CAAA,WAAQ;oCACR,YAAA,qNAAA,CAAA,aAAU;oCACV,MAAA,qMAAA,CAAA,OAAI;oCACJ,MAAA,qMAAA,CAAA,OAAI;oCACJ,YAAA,qNAAA,CAAA,aAAU;oCACV,QAAA,yMAAA,CAAA,SAAM;oCACN,OAAA,uMAAA,CAAA,QAAK;oCACL,WAAA,mNAAA,CAAA,YAAS;oCACT,cAAA,yNAAA,CAAA,eAAY;oCACZ,cAAA,8OAAA,CAAA,eAAY;oCACZ,UAAA,iNAAA,CAAA,WAAQ;oCACR,WAAA,qNAAA,CAAA,YAAS;gCACX,CAAC,CAAC,KAAK,IAAI,CAAwC,IAAI,sMAAA,CAAA,OAAI;gCAE3D,MAAM,iBAAiB,CAAC;oCACtB,EAAE,cAAc;oCAChB,IAAI,KAAK,GAAG,EAAE;wCACZ,aAAa,KAAK,GAAG;wCACrB,iCAAiC;wCACjC,MAAM,SAAS,KAAK,GAAG,KAAK,aAAa,eAAe,AAAC,kBAA0B,OAAT,KAAK,GAAG;wCAClF,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI;oCACnC;gCACF;gCAEA,qBACE,6LAAC;8CACC,cAAA,6LAAC;wCACC,SAAS;wCACT,WAAW,AAAC,uFAIX,OAHC,AAAC,KAAK,GAAG,KAAK,cAAc,cAAc,cAAe,cAAc,KAAK,GAAG,GAC3E,6BACA;;0DAGN,6LAAC;gDAAc,WAAU;;;;;;0DACzB,6LAAC;0DAAM,KAAK,KAAK;;;;;;;;;;;;mCAVZ,KAAK,IAAI;;;;;4BActB;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;4CAAG,WAAU;;gDACX,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;gDAAY;;;;;;;;;;;;;8CAGpC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAW,AAAC,iEAAyF,OAAzB,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD,EAAE;kDAC5F,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;kCAO5B,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAyC,OAAO;gDAAC,OAAO;4CAAa;;8DAClF,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,aAAa,WAAW;;sEAGxC,6LAAC,sMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,YAAY,WAAW;;sEAGvC,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,gBAAgB,WAAW;;sEAG3C,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,cAAc,WAAW;;sEAGzC,6LAAC,+MAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,YAAY,WAAW;;sEAGvC,6LAAC,sMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,YAAY,WAAW;;sEAGvC,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;8DAE9B,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAW,AAAC,6BAEX,OADC,cAAc,aAAa,WAAW;;sEAGxC,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAmB;;;;;;sEACnC,6LAAC;4DAAK,WAAU;sEAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAMnC,cAAc,4BACb;;wCAEG,6BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAyD;;;;;;0EAGvE,6LAAC;gEAAE,WAAU;0EAAqD;;;;;;0EAGlE,6LAAC;gEACC,SAAS,IAAM,eAAe;gEAC9B,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;sDAST,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;;wDAAqC;wDAClC,KAAK,IAAI,IAAI;wDAAO;;;;;;;8DAErC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAqD;;;;;;8EACnE,6LAAC;oEAAE,WAAU;8EAA4C,KAAK,YAAY,CAAC,cAAc;;;;;;;;;;;;sEAE3F,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAqD;;;;;;8EACnE,6LAAC;oEAAE,WAAU;8EAA4C,KAAK,KAAK,CAAC,MAAM;;;;;;;;;;;;sEAE5E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAqD;;;;;;8EACnE,6LAAC;oEAAE,WAAU;8EAA4C,IAAI,KAAK,KAAK,QAAQ,EAAE,WAAW;;;;;;;;;;;;;;;;;;;;;;;;sDAMlG,6LAAC;4CAAI,WAAU;sDACZ,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;gDACzB,MAAM,gBAAgB;oDACpB,MAAA,sMAAA,CAAA,OAAI;oDACJ,UAAU,mNAAA,CAAA,YAAS;oDACnB,WAAA,mNAAA,CAAA,YAAS;oDACT,OAAA,uMAAA,CAAA,QAAK;oDACL,UAAA,iNAAA,CAAA,WAAQ;oDACR,QAAA,yMAAA,CAAA,SAAM;oDACN,OAAA,uMAAA,CAAA,QAAK;oDACL,UAAA,6MAAA,CAAA,WAAQ;oDACR,YAAA,qNAAA,CAAA,aAAU;oDACV,MAAA,qMAAA,CAAA,OAAI;gDACN,CAAC,CAAC,KAAK,IAAI,CAAwC,IAAI,sMAAA,CAAA,OAAI;gDAE3D,qBACE,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAc,WAAU;;;;;;;;;;;0EAE3B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAG,WAAU;kFAA6D,KAAK,KAAK;;;;;;kFACrF,6LAAC;wEAAE,WAAU;kFAAmC;;;;;;;;;;;;;;;;;;mDAV/C,KAAK,IAAI;;;;;4CAepB;;;;;;wCAID,eAAe,6BAAe,6LAAC,qKAAA,CAAA,UAAkB;;;;;wCACjD,eAAe,0BAAY,6LAAC,+JAAA,CAAA,UAAe;;;;;wCAC3C,eAAe,yBAAW,6LAAC,6JAAA,CAAA,UAAc;;;;;wCACzC,eAAe,qCAAuB,6LAAC,uLAAA,CAAA,UAAyB;;;;;wCAChE,eAAe,oCAAsB,6LAAC,qLAAA,CAAA,UAAwB;;;;;;;gCAKlE,cAAc,2BAAa,6LAAC,gJAAA,CAAA,UAAY;;;;;gCAGxC,cAAc,+BAAiB,6LAAC,+IAAA,CAAA,UAAW;;;;;gCAG3C,cAAc,6BAAe,6LAAC,oJAAA,CAAA,UAAc;;;;;gCAG5C,cAAc,2BAAa,6LAAC,kJAAA,CAAA,UAAY;;;;;gCAGxC,cAAc,2BAAa,6LAAC,kJAAA,CAAA,UAAY;;;;;gCAGxC,cAAc,4BAAc,6LAAC,mJAAA,CAAA,UAAa;;;;;gCAG1C,cAAc,8BAAgB,6LAAC,qJAAA,CAAA,UAAe;;;;;gCAE9C,cAAc,wBAAU,6LAAC,+IAAA,CAAA,UAAS;;;;;gCAElC,cAAc,iCAAmB,6LAAC,wJAAA,CAAA,UAAkB;;;;;gCAEpD,cAAc,6BACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,4BAAc,6LAAC,mJAAA,CAAA,UAAa;;;;;gCAE1C,cAAc,8BAAgB,6LAAC,oJAAA,CAAA,UAAc;;;;;gCAE7C,cAAc,+BAAiB,6LAAC,sJAAA,CAAA,UAAgB;;;;;gCAEhD,cAAc,+BAAiB,6LAAC,sJAAA,CAAA,UAAgB;;;;;gCAEhD,cAAc,6BAAe,6LAAC,oJAAA,CAAA,UAAc;;;;;gCAE5C,cAAc,0BAAY,6LAAC,iJAAA,CAAA,UAAW;;;;;gCAEtC,cAAc,6BACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;gCAIhC,cAAc,2BAAa,6LAAC,kJAAA,CAAA,UAAY;;;;;gCAExC,cAAc,2BAAa,6LAAC,kJAAA,CAAA,UAAY;;;;;gCAExC,cAAc,6BAAe,6LAAC,oJAAA,CAAA,UAAc;;;;;gCAE5C,cAAc,0BAAY,6LAAC,iJAAA,CAAA,UAAW;;;;;gCAEtC,cAAc,6BAAe,6LAAC,oJAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzD;GA1dwB;;QACY,kIAAA,CAAA,UAAO;QAC1B,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAHd", "debugId": null}}]}