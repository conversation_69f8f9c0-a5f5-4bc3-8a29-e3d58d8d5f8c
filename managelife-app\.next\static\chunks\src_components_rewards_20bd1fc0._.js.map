{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/rewards/RewardCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { \n  Gift, \n  Coins, \n  Trophy, \n  Clock, \n  CheckCircle, \n  AlertCircle,\n  TrendingUp,\n  Calendar,\n  Star,\n  Zap\n} from 'lucide-react';\nimport { useRewards } from '@/hooks/useRewards';\nimport { formatCurrency, formatDate } from '@/utils';\nimport { RewardType } from '@/types';\n\nexport default function RewardCenter() {\n  const {\n    pendingRewards,\n    stats,\n    rules,\n    loading,\n    error,\n    claimReward,\n    claimAllRewards,\n    awardReward,\n    clearError,\n  } = useRewards();\n\n  const [claiming, setClaiming] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const handleClaimReward = async (rewardId: string) => {\n    setClaiming(rewardId);\n    try {\n      await claimReward(rewardId);\n      setSuccess('Reward claimed successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to claim reward:', error);\n    } finally {\n      setClaiming(null);\n    }\n  };\n\n  const handleClaimAll = async () => {\n    setClaiming('all');\n    try {\n      const result = await claimAllRewards();\n      setSuccess(`Claimed ${result.claimed.length} rewards totaling ${formatCurrency(result.total, 'MLIFE')}!`);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to claim all rewards:', error);\n    } finally {\n      setClaiming(null);\n    }\n  };\n\n  const handleTestReward = async (type: RewardType) => {\n    try {\n      await awardReward(type);\n      setSuccess(`${type.replace('_', ' ')} reward awarded!`);\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error: any) {\n      console.error('Failed to award test reward:', error);\n    }\n  };\n\n  const getRewardIcon = (type: RewardType) => {\n    switch (type) {\n      case 'welcome_bonus':\n        return <Gift className=\"w-5 h-5\" />;\n      case 'daily_login':\n        return <Calendar className=\"w-5 h-5\" />;\n      case 'rent_payment':\n        return <Coins className=\"w-5 h-5\" />;\n      case 'property_listing':\n        return <TrendingUp className=\"w-5 h-5\" />;\n      case 'referral':\n        return <Star className=\"w-5 h-5\" />;\n      case 'kyc_completion':\n        return <CheckCircle className=\"w-5 h-5\" />;\n      default:\n        return <Zap className=\"w-5 h-5\" />;\n    }\n  };\n\n  const getRewardColor = (type: RewardType) => {\n    switch (type) {\n      case 'welcome_bonus':\n        return 'text-purple-600 bg-purple-100';\n      case 'daily_login':\n        return 'text-blue-600 bg-blue-100';\n      case 'rent_payment':\n        return 'text-green-600 bg-green-100';\n      case 'property_listing':\n        return 'text-orange-600 bg-orange-100';\n      case 'referral':\n        return 'text-yellow-600 bg-yellow-100';\n      case 'kyc_completion':\n        return 'text-indigo-600 bg-indigo-100';\n      default:\n        return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-2xl font-bold text-gray-900 flex items-center\">\n          <Gift className=\"w-7 h-7 mr-3 text-blue-600\" />\n          Reward Center\n        </h2>\n      </div>\n\n      {/* Error/Success Messages */}\n      {error && (\n        <div className=\"p-4 bg-red-50 border border-red-200 rounded-lg flex items-center\">\n          <AlertCircle className=\"w-5 h-5 text-red-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-red-700 text-sm\">{error}</p>\n          <button\n            onClick={clearError}\n            className=\"ml-auto text-red-600 hover:text-red-700\"\n          >\n            ×\n          </button>\n        </div>\n      )}\n\n      {success && (\n        <div className=\"p-4 bg-green-50 border border-green-200 rounded-lg flex items-center\">\n          <CheckCircle className=\"w-5 h-5 text-green-600 mr-3 flex-shrink-0\" />\n          <p className=\"text-green-700 text-sm\">{success}</p>\n        </div>\n      )}\n\n      {/* Stats Overview */}\n      {stats && (\n        <div className=\"grid md:grid-cols-4 gap-6\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6 text-center\">\n            <Coins className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-blue-600\">{formatCurrency(stats.totalEarned, 'MLIFE')}</p>\n            <p className=\"text-sm text-blue-700\">Total Earned</p>\n          </div>\n          <div className=\"bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6 text-center\">\n            <CheckCircle className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-green-600\">{formatCurrency(stats.totalClaimed, 'MLIFE')}</p>\n            <p className=\"text-sm text-green-700\">Total Claimed</p>\n          </div>\n          <div className=\"bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-6 text-center\">\n            <Clock className=\"w-8 h-8 text-orange-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-orange-600\">{formatCurrency(stats.pendingRewards, 'MLIFE')}</p>\n            <p className=\"text-sm text-orange-700\">Pending</p>\n          </div>\n          <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-6 text-center\">\n            <Trophy className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n            <p className=\"text-2xl font-bold text-purple-600\">{stats.streakDays}</p>\n            <p className=\"text-sm text-purple-700\">Day Streak</p>\n          </div>\n        </div>\n      )}\n\n      {/* Pending Rewards */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n            <Clock className=\"w-5 h-5 mr-2 text-orange-600\" />\n            Pending Rewards ({pendingRewards.length})\n          </h3>\n          {pendingRewards.length > 0 && (\n            <button\n              onClick={handleClaimAll}\n              disabled={claiming === 'all' || loading}\n              className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50\"\n            >\n              {claiming === 'all' ? 'Claiming...' : 'Claim All'}\n            </button>\n          )}\n        </div>\n\n        {pendingRewards.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <Gift className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">No Pending Rewards</h4>\n            <p className=\"text-gray-600\">Complete activities to earn $MLIFE rewards!</p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {pendingRewards.map((reward) => (\n              <div\n                key={reward.id}\n                className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <div className=\"flex items-center space-x-4\">\n                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getRewardColor(reward.source)}`}>\n                    {getRewardIcon(reward.source)}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{reward.description}</h4>\n                    <p className=\"text-sm text-gray-600\">\n                      {formatCurrency(reward.amount, 'MLIFE')} • {formatDate(reward.createdAt)}\n                    </p>\n                  </div>\n                </div>\n                <button\n                  onClick={() => handleClaimReward(reward.id)}\n                  disabled={claiming === reward.id || loading}\n                  className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50\"\n                >\n                  {claiming === reward.id ? 'Claiming...' : 'Claim'}\n                </button>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Available Rewards */}\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-6 flex items-center\">\n          <Star className=\"w-5 h-5 mr-2 text-yellow-600\" />\n          Available Rewards\n        </h3>\n\n        <div className=\"grid md:grid-cols-2 gap-4\">\n          {rules.filter(rule => rule.isActive).map((rule) => (\n            <div\n              key={rule.id}\n              className=\"p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors\"\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex items-center space-x-3\">\n                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${getRewardColor(rule.type)}`}>\n                    {getRewardIcon(rule.type)}\n                  </div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900\">{rule.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{rule.description}</p>\n                    <p className=\"text-sm font-medium text-blue-600 mt-1\">\n                      {formatCurrency(rule.amount, 'MLIFE')}\n                    </p>\n                  </div>\n                </div>\n                {/* Test button for demo purposes */}\n                <button\n                  onClick={() => handleTestReward(rule.type)}\n                  className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded hover:bg-gray-200 transition-colors\"\n                >\n                  Test\n                </button>\n              </div>\n              \n              {rule.cooldownPeriod && (\n                <p className=\"text-xs text-gray-500 mt-2\">\n                  Cooldown: {rule.cooldownPeriod} hours\n                </p>\n              )}\n              \n              {rule.maxClaims && (\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  Max claims: {rule.maxClaims}\n                </p>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;;;AAhBA;;;;;AAmBe,SAAS;;IACtB,MAAM,EACJ,cAAc,EACd,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,WAAW,EACX,eAAe,EACf,WAAW,EACX,UAAU,EACX,GAAG,CAAA,GAAA,6HAAA,CAAA,aAAU,AAAD;IAEb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,MAAM,oBAAoB,OAAO;QAC/B,YAAY;QACZ,IAAI;YACF,MAAM,YAAY;YAClB,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,YAAY;QACZ,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,WAAW,AAAC,WAAoD,OAA1C,OAAO,OAAO,CAAC,MAAM,EAAC,sBAA0D,OAAtC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,EAAE,UAAS;YACtG,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,YAAY;YAClB,WAAW,AAAC,GAAyB,OAAvB,KAAK,OAAO,CAAC,KAAK,MAAK;YACrC,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,qNAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;YAC/B,KAAK;gBACH,qBAAO,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;QAC1B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBAA+B;;;;;;;;;;;;YAMlD,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMJ,yBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8NAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;YAK1C,uBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAE,WAAU;0CAAoC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;0CACnF,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAE,WAAU;0CAAqC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY,EAAE;;;;;;0CACrF,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAExC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAE,WAAU;0CAAsC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,cAAc,EAAE;;;;;;0CACxF,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCAAE,WAAU;0CAAsC,MAAM,UAAU;;;;;;0CACnE,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;0BAM7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiC;oCAChC,eAAe,MAAM;oCAAC;;;;;;;4BAEzC,eAAe,MAAM,GAAG,mBACvB,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,SAAS;gCAChC,WAAU;0CAET,aAAa,QAAQ,gBAAgB;;;;;;;;;;;;oBAK3C,eAAe,MAAM,KAAK,kBACzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAG/B,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,yDAAsF,OAA9B,eAAe,OAAO,MAAM;0DAClG,cAAc,OAAO,MAAM;;;;;;0DAE9B,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAA+B,OAAO,WAAW;;;;;;kEAC/D,6LAAC;wDAAE,WAAU;;4DACV,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,MAAM,EAAE;4DAAS;4DAAI,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;;;;;;;;;;;;;;;;;;;kDAI7E,6LAAC;wCACC,SAAS,IAAM,kBAAkB,OAAO,EAAE;wCAC1C,UAAU,aAAa,OAAO,EAAE,IAAI;wCACpC,WAAU;kDAET,aAAa,OAAO,EAAE,GAAG,gBAAgB;;;;;;;+BAnBvC,OAAO,EAAE;;;;;;;;;;;;;;;;0BA4BxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiC;;;;;;;kCAInD,6LAAC;wBAAI,WAAU;kCACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,GAAG,CAAC,CAAC,qBACxC,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,uDAAgF,OAA1B,eAAe,KAAK,IAAI;kEAC5F,cAAc,KAAK,IAAI;;;;;;kEAE1B,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA+B,KAAK,IAAI;;;;;;0EACtD,6LAAC;gEAAE,WAAU;0EAAyB,KAAK,WAAW;;;;;;0EACtD,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,EAAE;;;;;;;;;;;;;;;;;;0DAKnC,6LAAC;gDACC,SAAS,IAAM,iBAAiB,KAAK,IAAI;gDACzC,WAAU;0DACX;;;;;;;;;;;;oCAKF,KAAK,cAAc,kBAClB,6LAAC;wCAAE,WAAU;;4CAA6B;4CAC7B,KAAK,cAAc;4CAAC;;;;;;;oCAIlC,KAAK,SAAS,kBACb,6LAAC;wCAAE,WAAU;;4CAA6B;4CAC3B,KAAK,SAAS;;;;;;;;+BAjC1B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AA0C1B;GA9PwB;;QAWlB,6HAAA,CAAA,aAAU;;;KAXQ", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/MLUI/managelife-app/src/components/rewards/Leaderboard.tsx"], "sourcesContent": ["'use client';\n\nimport { Trophy, Medal, Award, Crown, TrendingUp } from 'lucide-react';\nimport { useLeaderboard } from '@/hooks/useRewards';\nimport { formatCurrency } from '@/utils';\n\nexport default function Leaderboard() {\n  const { leaderboard, loading, error } = useLeaderboard();\n\n  const getRankIcon = (rank: number) => {\n    switch (rank) {\n      case 1:\n        return <Crown className=\"w-6 h-6 text-yellow-500\" />;\n      case 2:\n        return <Medal className=\"w-6 h-6 text-gray-400\" />;\n      case 3:\n        return <Award className=\"w-6 h-6 text-amber-600\" />;\n      default:\n        return <span className=\"w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-600\">#{rank}</span>;\n    }\n  };\n\n  const getRankBadge = (rank: number) => {\n    switch (rank) {\n      case 1:\n        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';\n      case 2:\n        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';\n      case 3:\n        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center mb-6\">\n          <Trophy className=\"w-6 h-6 mr-3 text-yellow-600\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">Leaderboard</h3>\n        </div>\n        <div className=\"space-y-4\">\n          {[...Array(5)].map((_, i) => (\n            <div key={i} className=\"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg animate-pulse\">\n              <div className=\"w-8 h-8 bg-gray-300 rounded-full\"></div>\n              <div className=\"flex-1\">\n                <div className=\"h-4 bg-gray-300 rounded w-1/3 mb-2\"></div>\n                <div className=\"h-3 bg-gray-300 rounded w-1/4\"></div>\n              </div>\n              <div className=\"h-4 bg-gray-300 rounded w-20\"></div>\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n        <div className=\"flex items-center mb-6\">\n          <Trophy className=\"w-6 h-6 mr-3 text-yellow-600\" />\n          <h3 className=\"text-lg font-semibold text-gray-900\">Leaderboard</h3>\n        </div>\n        <div className=\"text-center py-8\">\n          <TrendingUp className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n          <p className=\"text-gray-600\">Failed to load leaderboard</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"mobile-card p-4 sm:p-6\">\n      <div className=\"flex items-center mb-4 sm:mb-6\">\n        <Trophy className=\"w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-yellow-600\" />\n        <h3 className=\"text-base sm:text-lg font-semibold text-gray-900\">Top Earners</h3>\n      </div>\n\n      {leaderboard.length === 0 ? (\n        <div className=\"text-center py-6 sm:py-8\">\n          <Trophy className=\"w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4\" />\n          <h4 className=\"text-base sm:text-lg font-semibold text-gray-900 mb-2\">No Data Yet</h4>\n          <p className=\"text-gray-600 mobile-text-responsive\">Be the first to earn rewards!</p>\n        </div>\n      ) : (\n        <div className=\"space-y-2 sm:space-y-3\">\n          {leaderboard.map((entry, index) => {\n            const rank = index + 1;\n            return (\n              <div\n                key={entry.userId}\n                className={`mobile-card p-3 sm:p-4 transition-all duration-200 ${\n                  rank <= 3\n                    ? 'bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200'\n                    : 'bg-gray-50 hover:bg-gray-100'\n                }`}\n              >\n                {/* Mobile Layout */}\n                <div className=\"flex items-start space-x-3 sm:hidden\">\n                  {/* Rank */}\n                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${getRankBadge(rank)}`}>\n                    {rank <= 3 ? getRankIcon(rank) : <span className=\"font-bold text-xs\">#{rank}</span>}\n                  </div>\n\n                  {/* User Info and Stats */}\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <span className=\"text-white font-semibold text-xs\">\n                          {entry.user?.name ? entry.user.name.split(' ').map(n => n[0]).join('') : 'U'}\n                        </span>\n                      </div>\n                      <div className=\"flex-1 min-w-0\">\n                        <h4 className=\"font-semibold text-gray-900 text-sm truncate\">\n                          {entry.user?.name || 'Anonymous User'}\n                        </h4>\n                        <p className=\"text-xs text-gray-600\">\n                          {entry.achievements?.length || 0} achievements\n                        </p>\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-between\">\n                      <div>\n                        <p className=\"font-bold text-sm text-blue-600\">\n                          {formatCurrency(entry.totalClaimed, 'MLIFE')}\n                        </p>\n                        <p className=\"text-xs text-gray-600\">\n                          {entry.streakDays} day streak\n                        </p>\n                      </div>\n\n                      {/* Champion badge for mobile */}\n                      {rank <= 3 && (\n                        <div className=\"flex-shrink-0\">\n                          {rank === 1 && (\n                            <span className=\"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium\">\n                              Champion\n                            </span>\n                          )}\n                          {rank === 2 && (\n                            <span className=\"text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-medium\">\n                              Runner-up\n                            </span>\n                          )}\n                          {rank === 3 && (\n                            <span className=\"text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full font-medium\">\n                              3rd Place\n                            </span>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Desktop Layout */}\n                <div className=\"hidden sm:flex items-center space-x-4\">\n                  {/* Rank */}\n                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getRankBadge(rank)}`}>\n                    {rank <= 3 ? getRankIcon(rank) : <span className=\"font-bold\">#{rank}</span>}\n                  </div>\n\n                  {/* User Info */}\n                  <div className=\"flex items-center space-x-3 flex-1\">\n                    <div className=\"w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center\">\n                      <span className=\"text-white font-semibold text-sm\">\n                        {entry.user?.name ? entry.user.name.split(' ').map(n => n[0]).join('') : 'U'}\n                      </span>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900\">\n                        {entry.user?.name || 'Anonymous User'}\n                      </h4>\n                      <p className=\"text-sm text-gray-600\">\n                        {entry.achievements?.length || 0} achievements\n                      </p>\n                    </div>\n                  </div>\n\n                  {/* Stats */}\n                  <div className=\"text-right\">\n                    <p className=\"font-bold text-lg text-blue-600\">\n                      {formatCurrency(entry.totalClaimed, 'MLIFE')}\n                    </p>\n                    <p className=\"text-sm text-gray-600\">\n                      {entry.streakDays} day streak\n                    </p>\n                  </div>\n\n                  {/* Special badges for top 3 */}\n                  {rank <= 3 && (\n                    <div className=\"flex flex-col items-center\">\n                      {rank === 1 && (\n                        <span className=\"text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium\">\n                          Champion\n                        </span>\n                      )}\n                      {rank === 2 && (\n                        <span className=\"text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-medium\">\n                          Runner-up\n                        </span>\n                      )}\n                      {rank === 3 && (\n                        <span className=\"text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full font-medium\">\n                          Third Place\n                        </span>\n                      )}\n                    </div>\n                  )}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* Footer */}\n      <div className=\"mt-6 pt-4 border-t border-gray-200\">\n        <p className=\"text-center text-sm text-gray-600\">\n          Earn more $MLIFE tokens by completing activities and climb the leaderboard!\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC;oBAAK,WAAU;;wBAA2E;wBAAE;;;;;;;QACxG;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAEtD,6LAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;4BAAY,WAAU;;8CACrB,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;;;;;;2BANP;;;;;;;;;;;;;;;;IAYpB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;8BAEtD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;;;;;;;YAGlE,YAAY,MAAM,KAAK,kBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC;wBAAG,WAAU;kCAAwD;;;;;;kCACtE,6LAAC;wBAAE,WAAU;kCAAuC;;;;;;;;;;;qCAGtD,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,OAAO;wBAuBR,aAKA,cAGA,qBAkDF,cAKA,cAGA;oBAxFb,MAAM,OAAO,QAAQ;oBACrB,qBACE,6LAAC;wBAEC,WAAW,AAAC,sDAIX,OAHC,QAAQ,IACJ,sEACA;;0CAIN,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAW,AAAC,uEAAyF,OAAnB,aAAa;kDACjG,QAAQ,IAAI,YAAY,sBAAQ,6LAAC;4CAAK,WAAU;;gDAAoB;gDAAE;;;;;;;;;;;;kDAIzE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,EAAA,cAAA,MAAM,IAAI,cAAV,kCAAA,YAAY,IAAI,IAAG,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM;;;;;;;;;;;kEAG7E,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,EAAA,eAAA,MAAM,IAAI,cAAV,mCAAA,aAAY,IAAI,KAAI;;;;;;0EAEvB,6LAAC;gEAAE,WAAU;;oEACV,EAAA,sBAAA,MAAM,YAAY,cAAlB,0CAAA,oBAAoB,MAAM,KAAI;oEAAE;;;;;;;;;;;;;;;;;;;0DAKvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EACV,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY,EAAE;;;;;;0EAEtC,6LAAC;gEAAE,WAAU;;oEACV,MAAM,UAAU;oEAAC;;;;;;;;;;;;;oDAKrB,QAAQ,mBACP,6LAAC;wDAAI,WAAU;;4DACZ,SAAS,mBACR,6LAAC;gEAAK,WAAU;0EAA2E;;;;;;4DAI5F,SAAS,mBACR,6LAAC;gEAAK,WAAU;0EAAuE;;;;;;4DAIxF,SAAS,mBACR,6LAAC;gEAAK,WAAU;0EAAyE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWrG,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAW,AAAC,2DAA6E,OAAnB,aAAa;kDACrF,QAAQ,IAAI,YAAY,sBAAQ,6LAAC;4CAAK,WAAU;;gDAAY;gDAAE;;;;;;;;;;;;kDAIjE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DACb,EAAA,eAAA,MAAM,IAAI,cAAV,mCAAA,aAAY,IAAI,IAAG,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM;;;;;;;;;;;0DAG7E,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,EAAA,eAAA,MAAM,IAAI,cAAV,mCAAA,aAAY,IAAI,KAAI;;;;;;kEAEvB,6LAAC;wDAAE,WAAU;;4DACV,EAAA,uBAAA,MAAM,YAAY,cAAlB,2CAAA,qBAAoB,MAAM,KAAI;4DAAE;;;;;;;;;;;;;;;;;;;kDAMvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,YAAY,EAAE;;;;;;0DAEtC,6LAAC;gDAAE,WAAU;;oDACV,MAAM,UAAU;oDAAC;;;;;;;;;;;;;oCAKrB,QAAQ,mBACP,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,mBACR,6LAAC;gDAAK,WAAU;0DAA2E;;;;;;4CAI5F,SAAS,mBACR,6LAAC;gDAAK,WAAU;0DAAuE;;;;;;4CAIxF,SAAS,mBACR,6LAAC;gDAAK,WAAU;0DAAyE;;;;;;;;;;;;;;;;;;;uBAlH5F,MAAM,MAAM;;;;;gBA2HvB;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAMzD;GA7NwB;;QACkB,6HAAA,CAAA,iBAAc;;;KADhC", "debugId": null}}]}