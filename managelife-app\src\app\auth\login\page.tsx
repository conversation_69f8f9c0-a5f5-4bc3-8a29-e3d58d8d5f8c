'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Building2, Mail, Lock, Wallet, ArrowLeft, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [loginMethod, setLoginMethod] = useState<'email' | 'wallet'>('email');
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const router = useRouter();
  const { login, loginWithWallet } = useAuth();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value,
    }));
    setError(''); // Clear error when user types
  };

  const handleEmailLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await login(formData.email, formData.password);
      router.push('/dashboard');
    } catch (error: any) {
      setError(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleWalletConnect = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Check if MetaMask is installed
      if (typeof window.ethereum !== 'undefined') {
        // Request account access
        const accounts = await window.ethereum.request({
          method: 'eth_requestAccounts',
        });

        if (accounts.length > 0) {
          const result = await loginWithWallet(accounts[0]);

          if (result.isNewUser) {
            // Redirect to profile setup for new users
            router.push('/dashboard?welcome=true');
          } else {
            router.push('/dashboard');
          }
        }
      } else {
        setError('Please install MetaMask to connect your wallet');
      }
    } catch (error: any) {
      console.error('Error connecting wallet:', error);
      setError(error.message || 'Wallet connection failed');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center mobile-safe-area">
      <div className="mobile-container w-full max-w-md">
        {/* Back to Home */}
        <Link
          href="/"
          className="touch-target inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors mb-6 sm:mb-8 px-2 py-2 rounded-md"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Home
        </Link>

        {/* Login Card */}
        <div className="mobile-card p-6 sm:p-8 shadow-xl">
          {/* Header */}
          <div className="text-center mb-6 sm:mb-8">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <img
                src="/logo/logo.png"
                alt="ManageLife"
                className="h-10 w-auto sm:h-12"
              />
            </div>
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Welcome Back</h1>
            <p className="text-gray-600 mobile-text-responsive">Sign in to your account to continue</p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg flex items-start">
              <AlertCircle className="w-5 h-5 text-red-600 mr-3 flex-shrink-0 mt-0.5" />
              <p className="text-red-700 text-sm mobile-text-responsive">{error}</p>
            </div>
          )}

          {/* Login Method Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1 mb-4 sm:mb-6">
            <button
              onClick={() => {
                setLoginMethod('email');
                setError('');
              }}
              className={`touch-target flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
                loginMethod === 'email'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Mail className="w-4 h-4 inline mr-2" />
              Email
            </button>
            <button
              onClick={() => {
                setLoginMethod('wallet');
                setError('');
              }}
              className={`touch-target flex-1 py-3 px-4 rounded-md text-sm font-medium transition-colors ${
                loginMethod === 'wallet'
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Wallet className="w-4 h-4 inline mr-2" />
              Wallet
            </button>
          </div>

          {/* Email Login Form */}
          {loginMethod === 'email' && (
            <form onSubmit={handleEmailLogin} className="mobile-space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    className="mobile-input w-full pl-10 pr-4"
                    placeholder="Enter your email"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    value={formData.password}
                    onChange={handleInputChange}
                    className="mobile-input w-full pl-10 pr-4"
                    placeholder="Enter your password"
                  />
                </div>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <label className="flex items-center touch-target">
                  <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 w-4 h-4" />
                  <span className="ml-2 text-sm text-gray-600 mobile-text-responsive">Remember me</span>
                </label>
                <Link href="/auth/forgot-password" className="touch-target text-sm text-blue-600 hover:text-blue-700 px-2 py-1 rounded-md">
                  Forgot password?
                </Link>
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="mobile-btn bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50"
              >
                {isLoading ? 'Signing In...' : 'Sign In'}
              </button>
            </form>
          )}

          {/* Wallet Connection */}
          {loginMethod === 'wallet' && (
            <div className="mobile-space-y-4">
              <div className="text-center py-6 sm:py-8">
                <Wallet className="w-12 h-12 sm:w-16 sm:h-16 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Connect Your Wallet</h3>
                <p className="text-gray-600 mb-6 mobile-text-responsive px-4 sm:px-0">
                  Connect your MetaMask wallet to access your ManageLife account
                </p>
                <button
                  onClick={handleWalletConnect}
                  disabled={isLoading}
                  className="mobile-btn bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300 web3-glow disabled:opacity-50"
                >
                  {isLoading ? 'Connecting...' : 'Connect MetaMask'}
                </button>
              </div>
            </div>
          )}

          {/* Sign Up Link */}
          <div className="text-center mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-gray-200">
            <p className="text-gray-600 mobile-text-responsive">
              Don't have an account?{' '}
              <Link href="/auth/register" className="text-blue-600 hover:text-blue-700 font-semibold touch-target inline-block px-1 py-1 rounded-md">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
