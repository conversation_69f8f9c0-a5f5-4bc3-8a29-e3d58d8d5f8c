'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Building2,
  Home,
  Search,
  Users,
  Calendar,
  TrendingUp,
  Coins,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  AlertCircle,
  CheckCircle,
  Gift,
  Bell,
  Briefcase,
  UserCircle,
  CreditCard,
  Wrench,
  Heart,
  HandCoins,
  ShoppingCart,
  FileBarChart,
  UserPlus,
  BarChart3
} from 'lucide-react';
import { UserRole } from '@/types';
import { DASHBOARD_NAV_ITEMS, getRoleDisplayName, getRoleColor } from '@/constants';
import { useAuth } from '@/contexts/AuthContext';
import RewardCenter from '@/components/rewards/RewardCenter';
import Leaderboard from '@/components/rewards/Leaderboard';
import SettingsPanel from '@/components/dashboard/SettingsPanel';
import PortfolioPanel from '@/components/dashboard/PortfolioPanel';
import RentalsPanel from '@/components/dashboard/RentalsPanel';
import ProfilePanel from '@/components/dashboard/ProfilePanel';
import NotificationsPanel from '@/components/dashboard/NotificationsPanel';
import PropertiesPanel from '@/components/dashboard/PropertiesPanel';
import NFTsPanel from '@/components/dashboard/NFTsPanel';
import PaymentsPanel from '@/components/dashboard/PaymentsPanel';
import MaintenancePanel from '@/components/dashboard/MaintenancePanel';
import MarketplacePanel from '@/components/dashboard/MarketplacePanel';
import FavoritesPanel from '@/components/dashboard/FavoritesPanel';
import OffersPanel from '@/components/dashboard/OffersPanel';
import TenantsPanel from '@/components/dashboard/TenantsPanel';
import ReportsPanel from '@/components/dashboard/ReportsPanel';
import CommunityPanel from '@/components/dashboard/CommunityPanel';
import EventsPanel from '@/components/dashboard/EventsPanel';
import ReferralsPanel from '@/components/dashboard/ReferralsPanel';
import NFTRentalPanel from '@/components/dashboard/NFTRentalPanel';
import HomeownerDashboard from '@/components/dashboard/homeowner/HomeownerDashboard';
import RenterDashboard from '@/components/dashboard/renter/RenterDashboard';
import BuyerDashboard from '@/components/dashboard/buyer/BuyerDashboard';
import PortfolioManagerDashboard from '@/components/dashboard/portfolio-manager/PortfolioManagerDashboard';
import CommunityMemberDashboard from '@/components/dashboard/community-member/CommunityMemberDashboard';

export default function DashboardPage() {
  const { user, loading, logout } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const isWelcome = searchParams.get('welcome') === 'true';

  const [activeRole, setActiveRole] = useState<UserRole>('buyer');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showWelcome, setShowWelcome] = useState(isWelcome);
  const [activeTab, setActiveTab] = useState<'overview' | 'rewards' | 'leaderboard' | 'portfolio' | 'rentals' | 'nft-rental' | 'settings' | 'profile' | 'properties' | 'nfts' | 'notifications' | 'analytics' | 'payments' | 'maintenance' | 'marketplace' | 'favorites' | 'offers' | 'purchases' | 'tenants' | 'reports' | 'community' | 'events' | 'referrals'>('overview');

  // Handle URL tab parameter
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    const validTabs = ['overview', 'rewards', 'leaderboard', 'portfolio', 'rentals', 'nft-rental', 'settings', 'profile', 'properties', 'nfts', 'notifications', 'analytics', 'payments', 'maintenance', 'marketplace', 'favorites', 'offers', 'purchases', 'tenants', 'reports', 'community', 'events', 'referrals'];
    if (tabParam && validTabs.includes(tabParam)) {
      setActiveTab(tabParam as any);
    }
  }, [searchParams]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  // Set initial active role when user data loads
  useEffect(() => {
    if (user && user.roles.length > 0) {
      setActiveRole(user.roles[0]);
    }
  }, [user]);

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error if no user
  if (!user) {
    return null; // Will redirect to login
  }

  const navItems = DASHBOARD_NAV_ITEMS[activeRole] || [];

  return (
    <div className="min-h-screen bg-gray-50 lg:flex mobile-safe-area">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-72 sm:w-80 lg:w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:z-auto lg:flex-shrink-0 ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex items-center justify-between h-14 sm:h-16 px-4 sm:px-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <img
              src="/logo/logo.png"
              alt="ManageLife"
              className="h-8 w-auto"
            />
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="touch-target lg:hidden text-gray-500 hover:text-gray-700 p-2 rounded-md"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-4 sm:p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white font-semibold text-sm">
                {user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}
              </span>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-gray-900 truncate">{user.name || 'User'}</h3>
              <p className="text-sm text-gray-600 truncate mobile-text-responsive">{user.email || user.walletAddress}</p>
              {user.walletAddress && (
                <p className="text-xs text-gray-500 font-mono">
                  {user.walletAddress.slice(0, 6)}...{user.walletAddress.slice(-4)}
                </p>
              )}
            </div>
          </div>

          {/* Role Selector */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Active Role</label>
            <select
              value={activeRole}
              onChange={(e) => setActiveRole(e.target.value as UserRole)}
              className="mobile-input w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {user.roles.map((role) => (
                <option key={role} value={role}>
                  {getRoleDisplayName(role)}
                </option>
              ))}
            </select>
          </div>

          {/* $MLIFE Balance */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700 mobile-text-responsive">$MLIFE Balance</span>
              <Coins className="w-4 h-4 text-blue-600" />
            </div>
            <p className="text-lg font-bold text-blue-600">{user.mlifeBalance.toLocaleString()}</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-6 py-4">
          <ul className="space-y-2">
            {navItems.map((item) => {
              const IconComponent = {
                Home,
                Building: Building2,
                Building2,
                Coins,
                FileText,
                Search,
                Users,
                Calendar,
                TrendingUp,
                Bell,
                Gift,
                CreditCard,
                Wrench,
                Heart,
                HandCoins,
                ShoppingCart,
                FileBarChart,
                UserPlus,
                BarChart3,
              }[item.icon as keyof typeof import('lucide-react')] || Home;

              const handleNavClick = (e: React.MouseEvent) => {
                e.preventDefault();
                if (item.tab) {
                  setActiveTab(item.tab as any);
                  // Update URL without page reload
                  const newUrl = item.tab === 'overview' ? '/dashboard' : `/dashboard?tab=${item.tab}`;
                  window.history.pushState({}, '', newUrl);
                }
              };

              return (
                <li key={item.href}>
                  <button
                    onClick={handleNavClick}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors text-left ${
                      (item.tab === 'overview' && activeTab === 'overview') || activeTab === item.tab
                        ? 'bg-blue-50 text-blue-600'
                        : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600'
                    }`}
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{item.label}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom Actions */}
        <div className="p-4 sm:p-6 border-t border-gray-200">
          <div className="space-y-2">
            <Link
              href="/settings"
              className="mobile-nav-item flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <Settings className="w-5 h-5" />
              <span>Settings</span>
            </Link>
            <button
              onClick={handleLogout}
              className="mobile-nav-item flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors w-full text-left"
            >
              <LogOut className="w-5 h-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 lg:flex lg:flex-col lg:min-w-0">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="mobile-container flex items-center justify-between h-14 sm:h-16">
            <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
              <button
                onClick={() => setSidebarOpen(true)}
                className="touch-target lg:hidden text-gray-500 hover:text-gray-700 p-2 rounded-md"
              >
                <Menu className="w-6 h-6" />
              </button>
              <h1 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">
                {getRoleDisplayName(activeRole)} Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-2 sm:space-x-4 flex-shrink-0">
              <span className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium ${getRoleColor(activeRole)}`}>
                {getRoleDisplayName(activeRole)}
              </span>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="mobile-container py-4 sm:py-6">
          <div className="max-w-7xl mx-auto">
            {/* Tab Navigation */}
            <div className="mb-6 sm:mb-8">
              <div className="mobile-nav-scroll-container w-full overflow-x-auto">
                <div className="mobile-dashboard-nav min-w-max mx-auto" style={{width: 'fit-content'}}>
                  <button
                    onClick={() => setActiveTab('overview')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'overview' ? 'active' : ''
                    }`}
                  >
                    <Home className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Overview</span>
                    <span className="sm:hidden">Home</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('rewards')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'rewards' ? 'active' : ''
                    }`}
                  >
                    <Gift className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Rewards</span>
                    <span className="sm:hidden">Coins</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('leaderboard')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'leaderboard' ? 'active' : ''
                    }`}
                  >
                    <TrendingUp className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Leaderboard</span>
                    <span className="sm:hidden">Rank</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('portfolio')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'portfolio' ? 'active' : ''
                    }`}
                  >
                    <Briefcase className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Portfolio</span>
                    <span className="sm:hidden">Assets</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('rentals')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'rentals' ? 'active' : ''
                    }`}
                  >
                    <Home className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Rentals</span>
                    <span className="sm:hidden">Rent</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('profile')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'profile' ? 'active' : ''
                    }`}
                  >
                    <UserCircle className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Profile</span>
                    <span className="sm:hidden">User</span>
                  </button>
                  <button
                    onClick={() => setActiveTab('settings')}
                    className={`mobile-dashboard-nav-item ${
                      activeTab === 'settings' ? 'active' : ''
                    }`}
                  >
                    <Settings className="w-4 h-4 mr-1 sm:mr-2 flex-shrink-0" />
                    <span className="hidden sm:inline">Settings</span>
                    <span className="sm:hidden">Config</span>
                  </button>
                </div>
              </div>
            </div>
            {/* Tab Content */}
            {activeTab === 'overview' && (
              <>
                {/* Welcome Message for New Users */}
                {showWelcome && (
                  <div className="mobile-card p-4 sm:p-6 mb-4 sm:mb-6 bg-green-50 border border-green-200">
                    <div className="flex items-start">
                      <CheckCircle className="w-5 h-5 sm:w-6 sm:h-6 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                      <div className="flex-1 min-w-0">
                        <h3 className="text-base sm:text-lg font-semibold text-green-900 mb-2">
                          Welcome to ManageLife! 🎉
                        </h3>
                        <p className="text-green-700 mb-3 sm:mb-4 mobile-text-responsive">
                          Your account has been created successfully. You've received 1,000 $MLIFE tokens as a welcome bonus!
                        </p>
                        <button
                          onClick={() => setShowWelcome(false)}
                          className="touch-target text-green-600 hover:text-green-700 font-medium text-sm px-2 py-1 rounded-md"
                        >
                          Dismiss
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Welcome Section */}
                <div className="mobile-card bg-gradient-to-r from-blue-600 to-purple-600 p-4 sm:p-6 lg:p-8 text-white mb-6 sm:mb-8">
                  <h2 className="text-xl sm:text-2xl font-bold mb-2">
                    Welcome back, {user.name || 'User'}!
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    <div className="bg-white/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm border border-white/10 hover:bg-white/25 transition-all duration-200">
                      <h3 className="font-semibold mb-1 text-sm sm:text-base text-white">$MLIFE Balance</h3>
                      <p className="text-lg sm:text-2xl font-bold text-white">{user.mlifeBalance.toLocaleString()}</p>
                    </div>
                    <div className="bg-white/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm border border-white/10 hover:bg-white/25 transition-all duration-200">
                      <h3 className="font-semibold mb-1 text-sm sm:text-base text-white">Active Roles</h3>
                      <p className="text-lg sm:text-2xl font-bold text-white">{user.roles.length}</p>
                    </div>
                    <div className="bg-white/20 rounded-lg p-3 sm:p-4 backdrop-blur-sm border border-white/10 hover:bg-white/25 transition-all duration-200">
                      <h3 className="font-semibold mb-1 text-sm sm:text-base text-white">Member Since</h3>
                      <p className="text-lg sm:text-2xl font-bold text-white">{new Date(user.joinedAt).getFullYear()}</p>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="mobile-grid mb-6 sm:mb-8">
                  {navItems.slice(1, 5).map((item) => {
                    const IconComponent = {
                      Home,
                      Building: Building2,
                      Building2,
                      Coins,
                      FileText,
                      Search,
                      Users,
                      Calendar,
                      TrendingUp,
                      Bell,
                    }[item.icon as keyof typeof import('lucide-react')] || Home;

                    return (
                      <Link
                        key={item.href}
                        href={item.href}
                        className="mobile-card p-4 sm:p-6 hover:shadow-md transition-shadow card-hover"
                      >
                        <div className="flex items-center space-x-3 sm:space-x-4">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <IconComponent className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-gray-900 text-sm sm:text-base truncate">{item.label}</h3>
                            <p className="text-xs sm:text-sm text-gray-600">Quick access</p>
                          </div>
                        </div>
                      </Link>
                    );
                  })}
                </div>

                {/* Role-specific dashboard */}
                {activeRole === 'homeowner' && <HomeownerDashboard />}
                {activeRole === 'renter' && <RenterDashboard />}
                {activeRole === 'buyer' && <BuyerDashboard />}
                {activeRole === 'portfolio-manager' && <PortfolioManagerDashboard />}
                {activeRole === 'community-member' && <CommunityMemberDashboard />}
              </>
            )}

            {/* Rewards Tab */}
            {activeTab === 'rewards' && <RewardCenter />}

            {/* Leaderboard Tab */}
            {activeTab === 'leaderboard' && <Leaderboard />}

            {/* Portfolio Tab */}
            {activeTab === 'portfolio' && <PortfolioPanel />}

            {/* Rentals Tab */}
            {activeTab === 'rentals' && <RentalsPanel />}

            {/* Profile Tab */}
            {activeTab === 'profile' && <ProfilePanel />}

            {/* Settings Tab */}
            {activeTab === 'settings' && <SettingsPanel />}

            {/* Additional Tabs */}
            {activeTab === 'properties' && <PropertiesPanel />}

            {activeTab === 'nfts' && <NFTsPanel />}

            {activeTab === 'notifications' && <NotificationsPanel />}

            {activeTab === 'analytics' && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Analytics</h2>
                <p className="text-gray-600">Advanced analytics dashboard coming soon.</p>
              </div>
            )}

            {activeTab === 'payments' && <PaymentsPanel />}

            {activeTab === 'nft-rental' && <NFTRentalPanel />}

            {activeTab === 'maintenance' && <MaintenancePanel />}

            {activeTab === 'marketplace' && <MarketplacePanel />}

            {activeTab === 'favorites' && <FavoritesPanel />}

            {activeTab === 'offers' && <OffersPanel />}

            {activeTab === 'purchases' && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Purchases</h2>
                <p className="text-gray-600">Purchase history and management coming soon.</p>
              </div>
            )}

            {activeTab === 'tenants' && <TenantsPanel />}

            {activeTab === 'reports' && <ReportsPanel />}

            {activeTab === 'community' && <CommunityPanel />}

            {activeTab === 'events' && <EventsPanel />}

            {activeTab === 'referrals' && <ReferralsPanel />}
          </div>
        </main>
      </div>
    </div>
  );
}
