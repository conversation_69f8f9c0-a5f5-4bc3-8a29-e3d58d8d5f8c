@import "tailwindcss";

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

@theme inline {
  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-primary: hsl(var(--primary));
  --color-secondary: hsl(var(--secondary));
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Custom ManageLife theme styles */
.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.web3-glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

/* Ensure buttons are clickable */
.bg-grid-pattern {
  pointer-events: none;
}

/* Button hover effects */
a:hover, button:hover {
  cursor: pointer;
}

/* Mobile-specific utilities */
.mobile-container {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .mobile-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Mobile touch targets - minimum 44px for accessibility */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Mobile-optimized buttons */
.mobile-btn {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.2s ease;
}

@media (max-width: 640px) {
  .mobile-btn {
    padding: 1rem 1.5rem;
    font-size: 1.125rem;
    width: 100%;
    justify-content: center;
  }
}

/* Mobile-optimized form inputs */
.mobile-input {
  padding: 0.875rem 1rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  transition: all 0.2s ease;
}

@media (max-width: 640px) {
  .mobile-input {
    padding: 1rem;
    font-size: 1.125rem;
  }
}

.mobile-input:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* Mobile-optimized cards */
.mobile-card {
  border-radius: 0.75rem;
  background: white;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  overflow: hidden;
  transition: all 0.3s ease;
}

@media (max-width: 640px) {
  .mobile-card {
    margin-left: -1rem;
    margin-right: -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}

.mobile-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Mobile navigation improvements */
.mobile-nav-item {
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  color: #374151;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  min-height: 44px;
}

.mobile-nav-item:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.mobile-nav-item.active {
  background-color: #dbeafe;
  color: #1d4ed8;
}

/* Mobile spacing utilities */
.mobile-space-y-4 > * + * {
  margin-top: 1rem;
}

@media (max-width: 640px) {
  .mobile-space-y-4 > * + * {
    margin-top: 1.5rem;
  }
}

.mobile-space-y-6 > * + * {
  margin-top: 1.5rem;
}

@media (max-width: 640px) {
  .mobile-space-y-6 > * + * {
    margin-top: 2rem;
  }
}

/* Mobile grid improvements */
.mobile-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .mobile-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mobile-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* Mobile text sizing */
.mobile-text-responsive {
  font-size: 1rem;
  line-height: 1.5;
}

@media (max-width: 640px) {
  .mobile-text-responsive {
    font-size: 0.875rem;
    line-height: 1.4;
  }
}

/* Mobile safe area for notched devices */
.mobile-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
