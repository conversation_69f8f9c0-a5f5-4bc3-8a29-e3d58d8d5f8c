'use client';

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, Building2, Co<PERSON>, Shield, Users, Zap, TrendingUp } from "lucide-react";

export default function Home() {
  // Button click handlers
  const handleWhitepaperClick = () => {
    console.log('Whitepaper button clicked');
    window.open('https://managelife.io/whitepaper', '_blank', 'noopener,noreferrer');
  };

  const handleWaitlistClick = () => {
    console.log('Waitlist button clicked');
    window.location.href = '/auth/register';
  };
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="mobile-container">
          <div className="flex justify-between items-center py-3 sm:py-4">
            <Link href="/" className="flex items-center space-x-2 touch-target">
              <img
                src="/logo/logo.png"
                alt="ManageLife"
                className="h-8 w-auto sm:h-10"
              />
            </Link>
            <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
              <Link href="/marketplace" className="mobile-nav-item text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-md text-sm font-medium">
                Marketplace
              </Link>
              <Link href="/about" className="mobile-nav-item text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-md text-sm font-medium">
                Team
              </Link>
              <a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="mobile-nav-item text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-md text-sm font-medium">
                Solutions
              </a>
              <Link href="/community" className="mobile-nav-item text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-md text-sm font-medium">
                Community
              </Link>
              <Link href="/docs" className="mobile-nav-item text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-md text-sm font-medium">
                Docs
              </Link>
              <a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="mobile-nav-item text-gray-600 hover:text-blue-600 transition-colors px-3 py-2 rounded-md text-sm font-medium">
                Official Site
              </a>
            </nav>
            <div className="flex items-center space-x-2 sm:space-x-4">
              <Link
                href="/auth/login"
                className="hidden sm:block text-gray-600 hover:text-blue-600 transition-colors touch-target px-3 py-2 rounded-md text-sm font-medium"
              >
                Sign In
              </Link>
              <Link
                href="/auth/register"
                className="mobile-btn bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 sm:px-4 rounded-lg hover:shadow-lg transition-all duration-300 web3-glow text-sm font-semibold"
              >
                <span className="hidden sm:inline">Get Started</span>
                <span className="sm:hidden">Join</span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50">
        <div className="absolute inset-0 bg-grid-pattern opacity-5 pointer-events-none"></div>
        <div className="mobile-container py-12 sm:py-16 lg:py-24 xl:py-32 relative z-10">
          <div className="text-center max-w-4xl mx-auto relative z-10">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              Real-World Ownership,
              <span className="block gradient-text">Reinvented</span>
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed px-4 sm:px-0">
              MLife bridges institutional-grade real estate with the accessibility and programmability of blockchain. Own real assets, earn real returns, and govern your portfolio from anywhere.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center px-4 sm:px-0">
              <button
                type="button"
                onClick={handleWhitepaperClick}
                className="mobile-btn bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:shadow-xl transition-all duration-300 web3-glow flex items-center justify-center cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full sm:w-auto"
              >
                <span className="hidden sm:inline">Download Whitepaper</span>
                <span className="sm:hidden">Get Whitepaper</span>
                <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
              </button>
              <button
                type="button"
                onClick={handleWaitlistClick}
                className="mobile-btn border-2 border-blue-600 text-blue-600 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 w-full sm:w-auto"
              >
                Join the Waitlist
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-12 sm:py-16 lg:py-20 bg-white">
        <div className="mobile-container">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4 sm:px-0">
              Revolutionary Real Estate Platform
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto px-4 sm:px-0 leading-relaxed">
              Experience the future of property investment with our comprehensive tokenization platform
            </p>
          </div>
          <div className="mobile-grid">
            <div className="mobile-card p-6 sm:p-8 card-hover">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                <Coins className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">NFT Tokenization</h3>
              <p className="text-gray-600 mobile-text-responsive">
                Convert your properties into NFTs (NFTi) and rental agreements into NFTr tokens for seamless blockchain-based management.
              </p>
            </div>
            <div className="mobile-card p-6 sm:p-8 card-hover">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">$MLIFE Rewards</h3>
              <p className="text-gray-600 mobile-text-responsive">
                Earn $MLIFE tokens for timely rent payments, successful transactions, and community participation.
              </p>
            </div>
            <div className="mobile-card p-6 sm:p-8 card-hover">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                <Shield className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Secure & Transparent</h3>
              <p className="text-gray-600 mobile-text-responsive">
                All transactions are secured by blockchain technology with full transparency and immutable records.
              </p>
            </div>
            <div className="mobile-card p-6 sm:p-8 card-hover">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                <Users className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Multi-Role Platform</h3>
              <p className="text-gray-600 mobile-text-responsive">
                Whether you're a homeowner, renter, buyer, or portfolio manager, we have tools designed for your needs.
              </p>
            </div>
            <div className="mobile-card p-6 sm:p-8 card-hover">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                <Zap className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Instant Transactions</h3>
              <p className="text-gray-600 mobile-text-responsive">
                Fast and efficient property transactions with smart contracts automating the entire process.
              </p>
            </div>
            <div className="mobile-card p-6 sm:p-8 card-hover">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4 sm:mb-6">
                <Building2 className="w-6 h-6 text-indigo-600" />
              </div>
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-3 sm:mb-4">Property Management</h3>
              <p className="text-gray-600 mobile-text-responsive">
                Comprehensive tools for managing properties, tenants, maintenance requests, and financial analytics.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-12 sm:py-16 lg:py-20 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="mobile-container text-center">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-4 sm:mb-6 px-4 sm:px-0 leading-tight">
            Ready to Transform Your Real Estate Portfolio?
          </h2>
          <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto px-4 sm:px-0 leading-relaxed">
            Join thousands of property owners, renters, and investors who are already using ManageLife to revolutionize their real estate experience.
          </p>
          <div className="px-4 sm:px-0">
            <Link
              href="/auth/register"
              className="mobile-btn bg-white text-blue-600 px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold hover:bg-gray-100 transition-all duration-300 inline-flex items-center justify-center w-full sm:w-auto"
            >
              Get Started Today
              <ArrowRight className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 sm:py-12">
        <div className="mobile-container">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            <div className="sm:col-span-2 lg:col-span-1">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Building2 className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold">ManageLife</span>
              </div>
              <p className="text-gray-400 mb-4 mobile-text-responsive">
                Revolutionizing real estate through blockchain technology and tokenization.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Platform</h3>
              <ul className="mobile-space-y-4 text-gray-400">
                <li><Link href="/marketplace" className="mobile-nav-item hover:text-white transition-colors block py-1">Marketplace</Link></li>
                <li><Link href="/dashboard" className="mobile-nav-item hover:text-white transition-colors block py-1">Dashboard</Link></li>
                <li><Link href="/tokenize" className="mobile-nav-item hover:text-white transition-colors block py-1">Tokenize Property</Link></li>
                <li><Link href="/rewards" className="mobile-nav-item hover:text-white transition-colors block py-1">Rewards</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Resources</h3>
              <ul className="mobile-space-y-4 text-gray-400">
                <li><Link href="/docs" className="mobile-nav-item hover:text-white transition-colors block py-1">Documentation</Link></li>
                <li><a href="https://managelife.io/whitepaper" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">Official Whitepaper</a></li>
                <li><Link href="/blog" className="mobile-nav-item hover:text-white transition-colors block py-1">Blog</Link></li>
                <li><a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">Official Website</a></li>
                <li><Link href="/support" className="mobile-nav-item hover:text-white transition-colors block py-1">Support</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Community</h3>
              <ul className="mobile-space-y-4 text-gray-400">
                <li><a href="https://www.facebook.com/ManageLife.IO" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">Facebook</a></li>
                <li><a href="https://x.com/ManageLife_io" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">Twitter/X</a></li>
                <li><a href="https://www.instagram.com/managelife.io/" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">Instagram</a></li>
                <li><a href="https://www.linkedin.com/company/managelife-io/" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">LinkedIn</a></li>
                <li><a href="https://managelife.io" target="_blank" rel="noopener noreferrer" className="mobile-nav-item hover:text-white transition-colors block py-1">Official Website</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-6 sm:mt-8 pt-6 sm:pt-8 text-center text-gray-400">
            <p className="mobile-text-responsive">&copy; 2025 ManageLife. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
