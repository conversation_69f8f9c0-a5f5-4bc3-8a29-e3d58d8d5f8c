'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Building2,
  <PERSON><PERSON><PERSON>,
  BarChart3,
  Calendar,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Users,
  Coins
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface Property {
  id: string;
  title: string;
  location: string;
  type: 'residential' | 'commercial' | 'industrial';
  value: number;
  purchasePrice: number;
  purchaseDate: string;
  monthlyIncome: number;
  occupancyRate: number;
  roi: number;
  status: 'active' | 'pending' | 'sold';
  image: string;
  tenants: number;
  maxTenants: number;
}

interface PortfolioStats {
  totalValue: number;
  totalInvestment: number;
  totalReturn: number;
  monthlyIncome: number;
  averageROI: number;
  propertiesCount: number;
  occupancyRate: number;
}

export default function PortfolioPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'analytics' | 'transactions'>('overview');
  const [filterType, setFilterType] = useState<'all' | 'residential' | 'commercial' | 'industrial'>('all');
  const [sortBy, setSortBy] = useState<'value' | 'roi' | 'income' | 'date'>('value');

  // Mock portfolio data
  const [portfolioStats] = useState<PortfolioStats>({
    totalValue: 2450000,
    totalInvestment: 1850000,
    totalReturn: 600000,
    monthlyIncome: 18500,
    averageROI: 12.5,
    propertiesCount: 8,
    occupancyRate: 92.3,
  });

  const [properties] = useState<Property[]>([
    {
      id: '1',
      title: 'Luxury Downtown Apartment',
      location: 'Manhattan, NY',
      type: 'residential',
      value: 850000,
      purchasePrice: 720000,
      purchaseDate: '2023-03-15',
      monthlyIncome: 6500,
      occupancyRate: 100,
      roi: 15.2,
      status: 'active',
      image: '/api/placeholder/300/200',
      tenants: 1,
      maxTenants: 1,
    },
    {
      id: '2',
      title: 'Modern Office Complex',
      location: 'Austin, TX',
      type: 'commercial',
      value: 1200000,
      purchasePrice: 950000,
      purchaseDate: '2022-11-20',
      monthlyIncome: 8200,
      occupancyRate: 85,
      roi: 18.7,
      status: 'active',
      image: '/api/placeholder/300/200',
      tenants: 12,
      maxTenants: 15,
    },
    {
      id: '3',
      title: 'Suburban Family Home',
      location: 'Phoenix, AZ',
      type: 'residential',
      value: 400000,
      purchasePrice: 350000,
      purchaseDate: '2023-07-10',
      monthlyIncome: 2800,
      occupancyRate: 100,
      roi: 11.4,
      status: 'active',
      image: '/api/placeholder/300/200',
      tenants: 1,
      maxTenants: 1,
    },
  ]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  // Allow all authenticated users to access portfolio page
  // Portfolio management is useful for all user types
  useEffect(() => {
    if (user) {
      console.log('User roles:', user.roles);
      console.log('User has access to portfolio page');
    }
  }, [user]);

  const filteredProperties = properties.filter(property => 
    filterType === 'all' || property.type === filterType
  );

  const sortedProperties = [...filteredProperties].sort((a, b) => {
    switch (sortBy) {
      case 'value':
        return b.value - a.value;
      case 'roi':
        return b.roi - a.roi;
      case 'income':
        return b.monthlyIncome - a.monthlyIncome;
      case 'date':
        return new Date(b.purchaseDate).getTime() - new Date(a.purchaseDate).getTime();
      default:
        return 0;
    }
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading portfolio...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">Portfolio Management</h1>
              <span className="bg-blue-100 text-blue-800 text-sm font-medium px-2.5 py-0.5 rounded">
                {user?.roles.includes('portfolio-manager') ? 'Portfolio Manager' :
                 user?.roles.includes('homeowner') ? 'Property Owner' : 'Investor'}
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
                <Plus className="w-4 h-4 mr-2" />
                Add Property
              </button>
              <button
                onClick={() => router.push('/dashboard')}
                className="text-gray-600 hover:text-blue-600 transition-colors"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Portfolio Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Portfolio Value</p>
                <p className="text-2xl font-bold text-gray-900">${portfolioStats.totalValue.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600 font-medium">+12.5%</span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Monthly Income</p>
                <p className="text-2xl font-bold text-gray-900">${portfolioStats.monthlyIncome.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Coins className="w-6 h-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600 font-medium">+8.2%</span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Average ROI</p>
                <p className="text-2xl font-bold text-gray-900">{portfolioStats.averageROI}%</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600 font-medium">+2.1%</span>
              <span className="text-sm text-gray-500 ml-1">vs last quarter</span>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
                <p className="text-2xl font-bold text-gray-900">{portfolioStats.occupancyRate}%</p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <Building2 className="w-6 h-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <TrendingDown className="w-4 h-4 text-red-500 mr-1" />
              <span className="text-sm text-red-600 font-medium">-1.2%</span>
              <span className="text-sm text-gray-500 ml-1">vs last month</span>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: PieChart },
                { id: 'properties', label: 'Properties', icon: Building2 },
                { id: 'analytics', label: 'Analytics', icon: BarChart3 },
                { id: 'transactions', label: 'Transactions', icon: Calendar },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Portfolio Distribution</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Residential</span>
                        <span className="font-medium">65%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '65%' }}></div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Commercial</span>
                        <span className="font-medium">30%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '30%' }}></div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">Industrial</span>
                        <span className="font-medium">5%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-purple-600 h-2 rounded-full" style={{ width: '5%' }}></div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">Rent payment received - $6,500</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">Property valuation updated</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">Maintenance request submitted</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">New tenant application</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'properties' && (
              <div className="space-y-6">
                {/* Filters */}
                <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                  <div className="flex flex-wrap gap-3">
                    <select
                      value={filterType}
                      onChange={(e) => setFilterType(e.target.value as any)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">All Types</option>
                      <option value="residential">Residential</option>
                      <option value="commercial">Commercial</option>
                      <option value="industrial">Industrial</option>
                    </select>
                    
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value as any)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="value">Sort by Value</option>
                      <option value="roi">Sort by ROI</option>
                      <option value="income">Sort by Income</option>
                      <option value="date">Sort by Date</option>
                    </select>
                  </div>
                  
                  <button className="flex items-center text-gray-600 hover:text-blue-600 transition-colors">
                    <Download className="w-4 h-4 mr-2" />
                    Export Report
                  </button>
                </div>

                {/* Properties Grid */}
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {sortedProperties.map((property) => (
                    <div key={property.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                      <img
                        src={property.image}
                        alt={property.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">{property.title}</h3>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            property.status === 'active' ? 'bg-green-100 text-green-800' :
                            property.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {property.status}
                          </span>
                        </div>
                        
                        <div className="flex items-center text-gray-600 mb-3">
                          <MapPin className="w-4 h-4 mr-1" />
                          <span className="text-sm">{property.location}</span>
                        </div>
                        
                        <div className="space-y-2 mb-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Current Value</span>
                            <span className="font-medium">${property.value.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Monthly Income</span>
                            <span className="font-medium text-green-600">${property.monthlyIncome.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">ROI</span>
                            <span className="font-medium text-blue-600">{property.roi}%</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Occupancy</span>
                            <span className="font-medium">{property.occupancyRate}%</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-600">
                            <Users className="w-4 h-4 mr-1" />
                            {property.tenants}/{property.maxTenants} tenants
                          </div>
                          <div className="flex space-x-2">
                            <button className="p-2 text-gray-400 hover:text-blue-600 transition-colors">
                              <Eye className="w-4 h-4" />
                            </button>
                            <button className="p-2 text-gray-400 hover:text-green-600 transition-colors">
                              <Edit className="w-4 h-4" />
                            </button>
                            <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'analytics' && (
              <div className="text-center py-12">
                <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Dashboard</h3>
                <p className="text-gray-600">Detailed analytics and performance metrics coming soon.</p>
              </div>
            )}

            {activeTab === 'transactions' && (
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Transaction History</h3>
                <p className="text-gray-600">Complete transaction history and financial reports coming soon.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
