'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Home,
  Calendar,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  MessageSquare,
  FileText,
  Wrench,
  CreditCard,
  Bell,
  MapPin,
  User,
  Phone,
  Mail,
  Plus
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface RentalProperty {
  id: string;
  title: string;
  address: string;
  rent: number;
  dueDate: string;
  status: 'current' | 'overdue' | 'paid';
  landlord: {
    name: string;
    email: string;
    phone: string;
  };
  leaseStart: string;
  leaseEnd: string;
  image: string;
}

interface MaintenanceRequest {
  id: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed';
  submittedDate: string;
  category: string;
}

interface PaymentHistory {
  id: string;
  amount: number;
  date: string;
  status: 'paid' | 'pending' | 'failed';
  method: string;
}

export default function RentalsPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'overview' | 'payments' | 'maintenance' | 'documents'>('overview');

  // Mock rental data
  const [currentRental] = useState<RentalProperty>({
    id: '1',
    title: 'Modern Downtown Apartment',
    address: '123 Main St, Apt 4B, New York, NY 10001',
    rent: 2800,
    dueDate: '2025-02-01',
    status: 'current',
    landlord: {
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
    },
    leaseStart: '2023-08-01',
    leaseEnd: '2025-07-31',
    image: '/api/placeholder/400/300',
  });

  const [maintenanceRequests] = useState<MaintenanceRequest[]>([
    {
      id: '1',
      title: 'Kitchen Faucet Leak',
      description: 'The kitchen faucet has been dripping constantly for the past week.',
      priority: 'medium',
      status: 'in_progress',
      submittedDate: '2025-01-15',
      category: 'Plumbing',
    },
    {
      id: '2',
      title: 'Heating System Issue',
      description: 'Heating system not working properly in the living room.',
      priority: 'high',
      status: 'pending',
      submittedDate: '2025-01-20',
      category: 'HVAC',
    },
    {
      id: '3',
      title: 'Light Bulb Replacement',
      description: 'Ceiling light in bedroom needs bulb replacement.',
      priority: 'low',
      status: 'completed',
      submittedDate: '2025-01-10',
      category: 'Electrical',
    },
  ]);

  const [paymentHistory] = useState<PaymentHistory[]>([
    {
      id: '1',
      amount: 2800,
      date: '2025-01-01',
      status: 'paid',
      method: 'Bank Transfer',
    },
    {
      id: '2',
      amount: 2800,
      date: '2023-12-01',
      status: 'paid',
      method: 'Credit Card',
    },
    {
      id: '3',
      amount: 2800,
      date: '2023-11-01',
      status: 'paid',
      method: 'Bank Transfer',
    },
  ]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  // Check if user has renter role
  useEffect(() => {
    if (user && !user.roles.includes('renter') && !user.roles.includes('homeowner')) {
      router.push('/dashboard');
    }
  }, [user, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Loading rental information...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect to login
  }

  const isRenter = user.roles.includes('renter');
  const isHomeowner = user.roles.includes('homeowner');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center space-x-4">
              <h1 className="text-2xl font-bold text-gray-900">
                {isRenter ? 'My Rental' : 'Rental Management'}
              </h1>
              <span className="bg-green-100 text-green-800 text-sm font-medium px-2.5 py-0.5 rounded">
                {isRenter ? 'Renter' : 'Homeowner'}
              </span>
            </div>
            <button
              onClick={() => router.push('/dashboard')}
              className="text-gray-600 hover:text-blue-600 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Current Rental Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="grid md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <div className="flex items-start space-x-4">
                <img
                  src={currentRental.image}
                  alt={currentRental.title}
                  className="w-24 h-24 rounded-lg object-cover"
                />
                <div className="flex-1">
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">{currentRental.title}</h2>
                  <div className="flex items-center text-gray-600 mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span className="text-sm">{currentRental.address}</span>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Lease: {new Date(currentRental.leaseStart).toLocaleDateString()} - {new Date(currentRental.leaseEnd).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-900">Monthly Rent</span>
                  <DollarSign className="w-4 h-4 text-blue-600" />
                </div>
                <p className="text-2xl font-bold text-blue-900">${currentRental.rent.toLocaleString()}</p>
                <p className="text-sm text-blue-700">Due: {new Date(currentRental.dueDate).toLocaleDateString()}</p>
              </div>

              {isRenter && (
                <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center">
                  <CreditCard className="w-4 h-4 mr-2" />
                  Pay Rent
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <button className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <CreditCard className="w-6 h-6 text-blue-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Pay Rent</p>
          </button>

          <button className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Wrench className="w-6 h-6 text-orange-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Maintenance</p>
          </button>

          <button className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <MessageSquare className="w-6 h-6 text-green-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Contact</p>
          </button>

          <button className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <FileText className="w-6 h-6 text-purple-600" />
            </div>
            <p className="text-sm font-medium text-gray-900">Documents</p>
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: Home },
                { id: 'payments', label: 'Payments', icon: DollarSign },
                { id: 'maintenance', label: 'Maintenance', icon: Wrench },
                { id: 'documents', label: 'Documents', icon: FileText },
              ].map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    {tab.label}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {activeTab === 'overview' && (
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Landlord Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                    <div className="flex items-center">
                      <User className="w-4 h-4 text-gray-600 mr-2" />
                      <span className="font-medium">{currentRental.landlord.name}</span>
                    </div>
                    <div className="flex items-center">
                      <Mail className="w-4 h-4 text-gray-600 mr-2" />
                      <span className="text-sm text-gray-600">{currentRental.landlord.email}</span>
                    </div>
                    <div className="flex items-center">
                      <Phone className="w-4 h-4 text-gray-600 mr-2" />
                      <span className="text-sm text-gray-600">{currentRental.landlord.phone}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Rent payment processed - $2,800</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Maintenance request submitted</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-gray-600">Lease renewal notice sent</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'payments' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Payment History</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                    Make Payment
                  </button>
                </div>

                <div className="space-y-3">
                  {paymentHistory.map((payment) => (
                    <div key={payment.id} className="bg-gray-50 rounded-lg p-4 flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`w-3 h-3 rounded-full ${
                          payment.status === 'paid' ? 'bg-green-500' :
                          payment.status === 'pending' ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}></div>
                        <div>
                          <p className="font-medium text-gray-900">${payment.amount.toLocaleString()}</p>
                          <p className="text-sm text-gray-600">{new Date(payment.date).toLocaleDateString()} • {payment.method}</p>
                        </div>
                      </div>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        payment.status === 'paid' ? 'bg-green-100 text-green-800' :
                        payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {payment.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'maintenance' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Maintenance Requests</h3>
                  <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
                    <Plus className="w-4 h-4 mr-2" />
                    New Request
                  </button>
                </div>

                <div className="space-y-4">
                  {maintenanceRequests.map((request) => (
                    <div key={request.id} className="bg-gray-50 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{request.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{request.description}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            request.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                            request.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                            request.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {request.priority}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                            request.status === 'completed' ? 'bg-green-100 text-green-800' :
                            request.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {request.status.replace('_', ' ')}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{request.category} • {new Date(request.submittedDate).toLocaleDateString()}</span>
                        <button className="text-blue-600 hover:text-blue-700 font-medium">
                          View Details
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Documents & Contracts</h3>
                <p className="text-gray-600">Lease agreements, receipts, and other documents will appear here.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
