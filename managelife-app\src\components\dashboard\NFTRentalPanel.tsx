'use client';

import { useState } from 'react';
import { 
  Coins,
  Calendar,
  Clock,
  DollarSign,
  TrendingUp,
  Star,
  Shield,
  Gift,
  Crown,
  Zap,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Download,
  Search,
  Filter,
  Grid3X3,
  List,
  Image,
  MapPin,
  Home,
  CreditCard,
  Award,
  Sparkles
} from 'lucide-react';

interface NFTRental {
  id: string;
  nftId: string;
  nftName: string;
  nftImage: string;
  propertyAddress: string;
  tokenId: string;
  contractAddress: string;
  blockchain: 'ethereum' | 'polygon' | 'binance';
  rentalPeriod: {
    start: string;
    end: string;
    duration: number; // in months
  };
  monthlyRent: number;
  totalPaid: number;
  status: 'active' | 'completed' | 'cancelled' | 'expired';
  membershipTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  benefits: string[];
  paymentHistory: {
    date: string;
    amount: number;
    status: 'paid' | 'pending' | 'failed';
    transactionHash?: string;
  }[];
  utilities: {
    electricity: boolean;
    water: boolean;
    internet: boolean;
    maintenance: boolean;
  };
  nftMetadata: {
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    attributes: { trait_type: string; value: string }[];
  };
}

interface MembershipBenefit {
  id: string;
  title: string;
  description: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  icon: string;
  isActive: boolean;
}

export default function NFTRentalPanel() {
  const [activeTab, setActiveTab] = useState<'overview' | 'rentals' | 'payments' | 'benefits'>('overview');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'completed' | 'cancelled' | 'expired'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock NFT rental data
  const [nftRentals] = useState<NFTRental[]>([
    {
      id: '1',
      nftId: 'nft-001',
      nftName: 'Downtown Loft #1234',
      nftImage: '/api/placeholder/300/300',
      propertyAddress: '456 Broadway, Downtown, NY 10013',
      tokenId: '1234',
      contractAddress: '0x1234...abcd',
      blockchain: 'ethereum',
      rentalPeriod: {
        start: '2025-01-01T00:00:00Z',
        end: '2025-12-31T23:59:59Z',
        duration: 12
      },
      monthlyRent: 2800,
      totalPaid: 8400,
      status: 'active',
      membershipTier: 'gold',
      benefits: ['Priority Support', 'Maintenance Included', 'Utility Credits', 'Exclusive Events'],
      paymentHistory: [
        { date: '2025-01-01T00:00:00Z', amount: 2800, status: 'paid', transactionHash: '0xabc123...' },
        { date: '2025-02-01T00:00:00Z', amount: 2800, status: 'paid', transactionHash: '0xdef456...' },
        { date: '2025-03-01T00:00:00Z', amount: 2800, status: 'paid', transactionHash: '0xghi789...' },
      ],
      utilities: {
        electricity: true,
        water: true,
        internet: true,
        maintenance: true
      },
      nftMetadata: {
        rarity: 'epic',
        attributes: [
          { trait_type: 'Location', value: 'Downtown' },
          { trait_type: 'Floor', value: '15' },
          { trait_type: 'View', value: 'City' },
          { trait_type: 'Furnished', value: 'Yes' }
        ]
      }
    },
    {
      id: '2',
      nftId: 'nft-002',
      nftName: 'Brooklyn Heights #5678',
      nftImage: '/api/placeholder/300/300',
      propertyAddress: '789 Heights Ave, Brooklyn, NY 11201',
      tokenId: '5678',
      contractAddress: '0x5678...efgh',
      blockchain: 'polygon',
      rentalPeriod: {
        start: '2023-06-01T00:00:00Z',
        end: '2025-05-31T23:59:59Z',
        duration: 12
      },
      monthlyRent: 3200,
      totalPaid: 32000,
      status: 'completed',
      membershipTier: 'platinum',
      benefits: ['VIP Support', 'All Utilities', 'Concierge Service', 'Exclusive Access'],
      paymentHistory: [
        { date: '2023-06-01T00:00:00Z', amount: 3200, status: 'paid', transactionHash: '0xjkl012...' },
        { date: '2023-07-01T00:00:00Z', amount: 3200, status: 'paid', transactionHash: '0xmno345...' },
        // ... more payments
      ],
      utilities: {
        electricity: true,
        water: true,
        internet: true,
        maintenance: true
      },
      nftMetadata: {
        rarity: 'legendary',
        attributes: [
          { trait_type: 'Location', value: 'Brooklyn Heights' },
          { trait_type: 'Floor', value: '20' },
          { trait_type: 'View', value: 'Manhattan Skyline' },
          { trait_type: 'Furnished', value: 'Luxury' }
        ]
      }
    },
    {
      id: '3',
      nftId: 'nft-003',
      nftName: 'Queens Studio #9012',
      nftImage: '/api/placeholder/300/300',
      propertyAddress: '321 Queens Blvd, Queens, NY 11375',
      tokenId: '9012',
      contractAddress: '0x9012...ijkl',
      blockchain: 'polygon',
      rentalPeriod: {
        start: '2025-02-01T00:00:00Z',
        end: '2025-07-31T23:59:59Z',
        duration: 6
      },
      monthlyRent: 1800,
      totalPaid: 3600,
      status: 'active',
      membershipTier: 'silver',
      benefits: ['Standard Support', 'Basic Maintenance', 'Community Access'],
      paymentHistory: [
        { date: '2025-02-01T00:00:00Z', amount: 1800, status: 'paid', transactionHash: '0xpqr678...' },
        { date: '2025-03-01T00:00:00Z', amount: 1800, status: 'paid', transactionHash: '0xstu901...' },
      ],
      utilities: {
        electricity: true,
        water: true,
        internet: false,
        maintenance: true
      },
      nftMetadata: {
        rarity: 'rare',
        attributes: [
          { trait_type: 'Location', value: 'Queens' },
          { trait_type: 'Floor', value: '3' },
          { trait_type: 'View', value: 'Street' },
          { trait_type: 'Furnished', value: 'Partial' }
        ]
      }
    }
  ]);

  // Mock membership benefits
  const [membershipBenefits] = useState<MembershipBenefit[]>([
    {
      id: '1',
      title: 'Priority Support',
      description: '24/7 priority customer support with dedicated account manager',
      tier: 'gold',
      icon: 'headphones',
      isActive: true
    },
    {
      id: '2',
      title: 'Maintenance Included',
      description: 'All maintenance and repairs included in rental price',
      tier: 'gold',
      icon: 'wrench',
      isActive: true
    },
    {
      id: '3',
      title: 'Utility Credits',
      description: 'Monthly utility credits up to $200',
      tier: 'gold',
      icon: 'zap',
      isActive: true
    },
    {
      id: '4',
      title: 'Exclusive Events',
      description: 'Access to exclusive community events and networking',
      tier: 'gold',
      icon: 'calendar',
      isActive: true
    },
    {
      id: '5',
      title: 'VIP Concierge',
      description: 'Personal concierge service for all your needs',
      tier: 'platinum',
      icon: 'crown',
      isActive: false
    }
  ]);

  const filteredRentals = nftRentals.filter(rental => {
    const matchesStatus = filterStatus === 'all' || rental.status === filterStatus;
    const matchesSearch = rental.nftName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rental.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rental.tokenId.includes(searchTerm);
    return matchesStatus && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'bronze':
        return 'bg-orange-100 text-orange-800';
      case 'silver':
        return 'bg-gray-100 text-gray-800';
      case 'gold':
        return 'bg-yellow-100 text-yellow-800';
      case 'platinum':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common':
        return 'bg-gray-100 text-gray-800';
      case 'rare':
        return 'bg-blue-100 text-blue-800';
      case 'epic':
        return 'bg-purple-100 text-purple-800';
      case 'legendary':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBlockchainIcon = (blockchain: string) => {
    switch (blockchain) {
      case 'ethereum':
        return '⟠';
      case 'polygon':
        return '⬟';
      case 'binance':
        return '🔶';
      default:
        return '🔗';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const totalRentals = nftRentals.length;
  const activeRentals = nftRentals.filter(r => r.status === 'active').length;
  const totalPaid = nftRentals.reduce((sum, r) => sum + r.totalPaid, 0);
  const currentTier = nftRentals.find(r => r.status === 'active')?.membershipTier || 'bronze';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">NFT Rental Membership</h2>
          <p className="text-gray-600">Manage your NFT rental properties and membership benefits</p>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getTierColor(currentTier)}`}>
            <Crown className="w-4 h-4 mr-1" />
            {currentTier.charAt(0).toUpperCase() + currentTier.slice(1)} Member
          </span>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Home className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Rentals</p>
              <p className="text-2xl font-bold text-gray-900">{totalRentals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Rentals</p>
              <p className="text-2xl font-bold text-gray-900">{activeRentals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Paid</p>
              <p className="text-2xl font-bold text-gray-900">${totalPaid.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
              <Crown className="w-6 h-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Membership Tier</p>
              <p className="text-2xl font-bold text-gray-900 capitalize">{currentTier}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: TrendingUp },
              { id: 'rentals', label: 'NFT Rentals', icon: Coins },
              { id: 'payments', label: 'Payment History', icon: CreditCard },
              { id: 'benefits', label: 'Membership Benefits', icon: Gift },
            ].map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Membership Status</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Current Tier</span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium ${getTierColor(currentTier)}`}>
                        <Crown className="w-4 h-4 mr-1" />
                        {currentTier.charAt(0).toUpperCase() + currentTier.slice(1)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Active Since</span>
                      <span className="font-semibold">{formatDate(nftRentals[0]?.rentalPeriod.start || '')}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Benefits Active</span>
                      <span className="font-semibold text-green-600">{membershipBenefits.filter(b => b.isActive).length}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                  <div className="space-y-3">
                    {nftRentals.slice(0, 3).map((rental) => (
                      <div key={rental.id} className="flex items-center space-x-3">
                        <img
                          src={rental.nftImage}
                          alt={rental.nftName}
                          className="w-10 h-10 rounded-lg object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">{rental.nftName}</p>
                          <p className="text-xs text-gray-500">{rental.status}</p>
                        </div>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
                          {rental.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* NFT Rentals Tab */}
          {activeTab === 'rentals' && (
            <div className="space-y-6">
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <div className="relative flex-1 max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="Search NFT rentals..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="expired">Expired</option>
                </select>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <Grid3X3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* NFT Rentals Grid/List */}
              {viewMode === 'grid' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {filteredRentals.map((rental) => (
                    <div key={rental.id} className="bg-white border border-gray-200 rounded-xl overflow-hidden hover:shadow-md transition-shadow">
                      <div className="relative">
                        <img
                          src={rental.nftImage}
                          alt={rental.nftName}
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute top-4 left-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
                            {rental.status}
                          </span>
                        </div>
                        <div className="absolute top-4 right-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRarityColor(rental.nftMetadata.rarity)}`}>
                            <Sparkles className="w-3 h-3 mr-1" />
                            {rental.nftMetadata.rarity}
                          </span>
                        </div>
                        <div className="absolute bottom-4 left-4">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-black bg-opacity-70 text-white">
                            {getBlockchainIcon(rental.blockchain)} {rental.blockchain}
                          </span>
                        </div>
                      </div>

                      <div className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-1">{rental.nftName}</h3>
                            <p className="text-sm text-gray-600 flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              {rental.propertyAddress}
                            </p>
                          </div>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTierColor(rental.membershipTier)}`}>
                            <Crown className="w-3 h-3 mr-1" />
                            {rental.membershipTier}
                          </span>
                        </div>

                        <div className="space-y-2 mb-4">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Token ID</span>
                            <span className="font-mono">#{rental.tokenId}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Monthly Rent</span>
                            <span className="font-semibold">${rental.monthlyRent.toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Total Paid</span>
                            <span className="font-semibold text-green-600">${rental.totalPaid.toLocaleString()}</span>
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                          <div className="text-xs text-gray-500">
                            <p>{formatDate(rental.rentalPeriod.start)} - {formatDate(rental.rentalPeriod.end)}</p>
                          </div>
                          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors flex items-center">
                            <Eye className="w-4 h-4 mr-1" />
                            View Details
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredRentals.map((rental) => (
                    <div key={rental.id} className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-sm transition-shadow">
                      <div className="flex items-start space-x-4">
                        <img
                          src={rental.nftImage}
                          alt={rental.nftName}
                          className="w-20 h-20 rounded-lg object-cover flex-shrink-0"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div>
                              <h3 className="text-lg font-semibold text-gray-900">{rental.nftName}</h3>
                              <p className="text-sm text-gray-600 flex items-center mt-1">
                                <MapPin className="w-4 h-4 mr-1" />
                                {rental.propertyAddress}
                              </p>
                              <div className="flex items-center space-x-4 text-sm text-gray-600 mt-2">
                                <span>Token #{rental.tokenId}</span>
                                <span>{getBlockchainIcon(rental.blockchain)} {rental.blockchain}</span>
                                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRarityColor(rental.nftMetadata.rarity)}`}>
                                  {rental.nftMetadata.rarity}
                                </span>
                              </div>
                            </div>
                            <div className="text-right">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
                                {rental.status}
                              </span>
                              <p className="text-lg font-bold text-gray-900 mt-2">${rental.monthlyRent.toLocaleString()}/mo</p>
                              <p className="text-sm text-green-600 font-medium">Total: ${rental.totalPaid.toLocaleString()}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {filteredRentals.length === 0 && (
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
                  <Coins className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No NFT rentals found</h3>
                  <p className="text-gray-600">
                    {searchTerm || filterStatus !== 'all'
                      ? 'Try adjusting your search or filters.'
                      : 'You haven\'t rented any NFT properties yet.'
                    }
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Payment History Tab */}
          {activeTab === 'payments' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Payment History</h3>
              <div className="space-y-4">
                {nftRentals.map((rental) => (
                  <div key={rental.id} className="bg-white border border-gray-200 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <img
                          src={rental.nftImage}
                          alt={rental.nftName}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div>
                          <h4 className="font-semibold text-gray-900">{rental.nftName}</h4>
                          <p className="text-sm text-gray-600">Token #{rental.tokenId}</p>
                        </div>
                      </div>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
                        {rental.status}
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      {rental.paymentHistory.map((payment, index) => (
                        <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                          <div className="flex items-center space-x-3">
                            <div className={`w-2 h-2 rounded-full ${
                              payment.status === 'paid' ? 'bg-green-500' : 
                              payment.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                            }`}></div>
                            <span className="text-sm text-gray-600">{formatDate(payment.date)}</span>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold">${payment.amount.toLocaleString()}</p>
                            {payment.transactionHash && (
                              <p className="text-xs text-gray-500 font-mono">{payment.transactionHash}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Membership Benefits Tab */}
          {activeTab === 'benefits' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Membership Benefits</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {membershipBenefits.map((benefit) => (
                  <div key={benefit.id} className={`border rounded-lg p-6 ${
                    benefit.isActive ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50'
                  }`}>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          benefit.isActive ? 'bg-blue-100' : 'bg-gray-200'
                        }`}>
                          <Gift className={`w-6 h-6 ${benefit.isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                        </div>
                        <div>
                          <h4 className={`font-semibold ${benefit.isActive ? 'text-gray-900' : 'text-gray-500'}`}>
                            {benefit.title}
                          </h4>
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getTierColor(benefit.tier)}`}>
                            {benefit.tier}
                          </span>
                        </div>
                      </div>
                      {benefit.isActive ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircle className="w-5 h-5 text-gray-400" />
                      )}
                    </div>
                    <p className={`text-sm ${benefit.isActive ? 'text-gray-700' : 'text-gray-500'}`}>
                      {benefit.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
