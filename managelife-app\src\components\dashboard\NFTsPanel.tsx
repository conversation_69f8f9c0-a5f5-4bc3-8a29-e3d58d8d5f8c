'use client';

import { useState } from 'react';
import { 
  Co<PERSON>,
  Eye,
  ExternalLink,
  Share2,
  Download,
  Star,
  TrendingUp,
  Calendar,
  DollarSign,
  Filter,
  Search,
  Grid3X3,
  List,
  Plus
} from 'lucide-react';

interface NFT {
  id: string;
  name: string;
  description: string;
  tokenId: string;
  contractAddress: string;
  imageUrl: string;
  propertyAddress: string;
  mintDate: string;
  currentValue: number;
  originalPrice: number;
  ownership: number; // percentage
  totalSupply: number;
  attributes: {
    trait_type: string;
    value: string;
  }[];
  blockchain: 'ethereum' | 'polygon';
  status: 'active' | 'listed' | 'sold';
}

export default function NFTsPanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'listed' | 'sold'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Mock NFTs data
  const [nfts] = useState<NFT[]>([
    {
      id: '1',
      name: 'Downtown Apartment #123',
      description: 'Tokenized ownership of a modern 2-bedroom apartment in downtown Manhattan',
      tokenId: '123',
      contractAddress: '0x1234...5678',
      imageUrl: '/api/placeholder/300/300',
      propertyAddress: '123 Main St, Downtown, NY 10001',
      mintDate: '2023-06-01T10:00:00Z',
      currentValue: 45000,
      originalPrice: 40000,
      ownership: 10,
      totalSupply: 100,
      attributes: [
        { trait_type: 'Property Type', value: 'Apartment' },
        { trait_type: 'Bedrooms', value: '2' },
        { trait_type: 'Bathrooms', value: '2' },
        { trait_type: 'Square Feet', value: '1200' },
        { trait_type: 'Location', value: 'Downtown' },
      ],
      blockchain: 'ethereum',
      status: 'active',
    },
    {
      id: '2',
      name: 'Luxury Condo #789',
      description: 'Premium condo with city view, tokenized for fractional ownership',
      tokenId: '789',
      contractAddress: '0x9876...5432',
      imageUrl: '/api/placeholder/300/300',
      propertyAddress: '789 Park Blvd, Uptown, NY 10003',
      mintDate: '2023-09-15T14:30:00Z',
      currentValue: 75000,
      originalPrice: 70000,
      ownership: 15,
      totalSupply: 200,
      attributes: [
        { trait_type: 'Property Type', value: 'Condo' },
        { trait_type: 'Bedrooms', value: '3' },
        { trait_type: 'Bathrooms', value: '2' },
        { trait_type: 'Square Feet', value: '1800' },
        { trait_type: 'Location', value: 'Uptown' },
        { trait_type: 'View', value: 'City' },
      ],
      blockchain: 'polygon',
      status: 'active',
    },
    {
      id: '3',
      name: 'Suburban House #456',
      description: 'Family house in quiet suburban neighborhood',
      tokenId: '456',
      contractAddress: '0x5555...7777',
      imageUrl: '/api/placeholder/300/300',
      propertyAddress: '456 Oak Ave, Suburbia, NY 10002',
      mintDate: '2023-12-01T09:15:00Z',
      currentValue: 32000,
      originalPrice: 35000,
      ownership: 8,
      totalSupply: 150,
      attributes: [
        { trait_type: 'Property Type', value: 'House' },
        { trait_type: 'Bedrooms', value: '4' },
        { trait_type: 'Bathrooms', value: '3' },
        { trait_type: 'Square Feet', value: '2400' },
        { trait_type: 'Location', value: 'Suburban' },
      ],
      blockchain: 'ethereum',
      status: 'listed',
    },
  ]);

  const filteredNFTs = nfts.filter(nft => {
    const matchesSearch = nft.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         nft.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || nft.status === filterStatus;
    
    return matchesSearch && matchesStatus;
  });

  const totalValue = nfts.reduce((sum, nft) => sum + nft.currentValue, 0);
  const totalGainLoss = nfts.reduce((sum, nft) => sum + (nft.currentValue - nft.originalPrice), 0);
  const totalOwnership = nfts.reduce((sum, nft) => sum + nft.ownership, 0);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'listed':
        return 'bg-blue-100 text-blue-800';
      case 'sold':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBlockchainColor = (blockchain: string) => {
    switch (blockchain) {
      case 'ethereum':
        return 'bg-purple-100 text-purple-800';
      case 'polygon':
        return 'bg-indigo-100 text-indigo-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">My NFTs</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Mint NFT
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Coins className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Portfolio Value</p>
              <p className="text-2xl font-bold text-gray-900">${totalValue.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
              totalGainLoss >= 0 ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <TrendingUp className={`w-6 h-6 ${
                totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'
              }`} />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Gain/Loss</p>
              <p className={`text-2xl font-bold ${
                totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {totalGainLoss >= 0 ? '+' : ''}${totalGainLoss.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Star className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total NFTs</p>
              <p className="text-2xl font-bold text-gray-900">{nfts.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search NFTs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="listed">Listed</option>
              <option value="sold">Sold</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Grid3X3 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* NFTs Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredNFTs.map((nft) => (
            <div key={nft.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
              <div className="relative">
                <img
                  src={nft.imageUrl}
                  alt={nft.name}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4 flex space-x-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(nft.status)}`}>
                    {nft.status}
                  </span>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBlockchainColor(nft.blockchain)}`}>
                    {nft.blockchain}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{nft.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{nft.description}</p>
                
                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Current Value:</span>
                    <span className="font-medium">${nft.currentValue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Ownership:</span>
                    <span className="font-medium">{nft.ownership}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Token ID:</span>
                    <span className="font-mono text-xs">{nft.tokenId}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                      <ExternalLink className="w-4 h-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                      <Share2 className="w-4 h-4" />
                    </button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Minted {new Date(nft.mintDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">NFT</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ownership</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Blockchain</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredNFTs.map((nft) => (
                  <tr key={nft.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <img className="h-12 w-12 rounded-lg object-cover" src={nft.imageUrl} alt={nft.name} />
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{nft.name}</div>
                          <div className="text-sm text-gray-500">Token #{nft.tokenId}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">${nft.currentValue.toLocaleString()}</div>
                      <div className={`text-sm ${
                        nft.currentValue >= nft.originalPrice ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {nft.currentValue >= nft.originalPrice ? '+' : ''}
                        ${(nft.currentValue - nft.originalPrice).toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {nft.ownership}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(nft.status)}`}>
                        {nft.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getBlockchainColor(nft.blockchain)}`}>
                        {nft.blockchain}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button className="text-blue-600 hover:text-blue-700">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="text-gray-600 hover:text-gray-700">
                          <ExternalLink className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {filteredNFTs.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Coins className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No NFTs found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterStatus !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'Get started by minting your first property NFT.'
            }
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Mint NFT
          </button>
        </div>
      )}
    </div>
  );
}
