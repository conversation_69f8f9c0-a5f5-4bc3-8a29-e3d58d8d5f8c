'use client';

import { useState } from 'react';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Globe,
  Camera,
  Edit,
  Save,
  X,
  Check,
  AlertTriangle,
  Shield,
  Wallet,
  Award,
  TrendingUp,
  Building2,
  Coins,
  DollarSign
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  bio: string;
  website: string;
  joinDate: string;
  avatar: string;
  roles: string[];
  verified: boolean;
  kycStatus: 'pending' | 'verified' | 'rejected';
}

interface UserStats {
  totalInvestment: number;
  portfolioValue: number;
  monthlyIncome: number;
  propertiesOwned: number;
  totalReturns: number;
  rewardPoints: number;
  membershipLevel: string;
  joinedCommunities: number;
}

export default function ProfilePanel() {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const [profile, setProfile] = useState<UserProfile>({
    id: user?.id || '1',
    name: user?.name || 'John Doe',
    email: user?.email || '<EMAIL>',
    phone: '+****************',
    location: 'New York, NY',
    bio: 'Real estate investor passionate about blockchain technology and decentralized finance.',
    website: 'https://johndoe.com',
    joinDate: '2023-01-15',
    avatar: '/api/placeholder/150/150',
    roles: user?.roles || ['homeowner'],
    verified: true,
    kycStatus: 'verified',
  });

  const [editedProfile, setEditedProfile] = useState(profile);

  const userStats: UserStats = {
    totalInvestment: 850000,
    portfolioValue: 1200000,
    monthlyIncome: 8500,
    propertiesOwned: 3,
    totalReturns: 350000,
    rewardPoints: 12500,
    membershipLevel: 'Gold',
    joinedCommunities: 5,
  };

  const handleSave = async () => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProfile(editedProfile);
      setIsEditing(false);
      setSaveMessage({ type: 'success', text: 'Profile updated successfully!' });
    } catch (error: any) {
      setSaveMessage({ type: 'error', text: error.message || 'Failed to update profile' });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditedProfile(profile);
    setIsEditing(false);
    setSaveMessage(null);
  };

  const getRoleDisplayName = (role: string) => {
    const roleMap: { [key: string]: string } = {
      'homeowner': 'Property Owner',
      'renter': 'Renter',
      'buyer': 'Investor',
      'portfolio-manager': 'Portfolio Manager',
      'community-member': 'Community Member',
    };
    return roleMap[role] || role;
  };

  const getKycStatusColor = (status: string) => {
    switch (status) {
      case 'verified': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Save Message */}
      {saveMessage && (
        <div className={`p-4 rounded-lg flex items-center ${
          saveMessage.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-700' 
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          {saveMessage.type === 'success' ? (
            <Check className="w-5 h-5 mr-3 flex-shrink-0" />
          ) : (
            <AlertTriangle className="w-5 h-5 mr-3 flex-shrink-0" />
          )}
          <p>{saveMessage.text}</p>
          <button
            onClick={() => setSaveMessage(null)}
            className="ml-auto text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      <div className="grid lg:grid-cols-3 gap-6">
        {/* Profile Information */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900">Profile Information</h2>
              {!isEditing ? (
                <button
                  onClick={() => setIsEditing(true)}
                  className="text-blue-600 hover:text-blue-800 font-medium flex items-center"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Edit Profile
                </button>
              ) : (
                <div className="flex space-x-2">
                  <button
                    onClick={handleCancel}
                    className="text-gray-600 hover:text-gray-800 font-medium flex items-center"
                  >
                    <X className="w-4 h-4 mr-1" />
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center"
                  >
                    {isSaving ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="w-4 h-4 mr-1" />
                        Save
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          </div>

          <div className="p-6">
            {/* Avatar and Basic Info */}
            <div className="flex items-start space-x-6 mb-8">
              <div className="relative">
                <img
                  src={profile.avatar}
                  alt={profile.name}
                  className="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg"
                />
                {isEditing && (
                  <button className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full hover:bg-blue-700 transition-colors">
                    <Camera className="w-4 h-4" />
                  </button>
                )}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  {isEditing ? (
                    <input
                      type="text"
                      value={editedProfile.name}
                      onChange={(e) => setEditedProfile(prev => ({ ...prev, name: e.target.value }))}
                      className="text-2xl font-bold text-gray-900 border-b-2 border-blue-500 bg-transparent focus:outline-none"
                    />
                  ) : (
                    <h1 className="text-2xl font-bold text-gray-900">{profile.name}</h1>
                  )}
                  
                  {profile.verified && (
                    <div className="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                      <Shield className="w-3 h-3 mr-1" />
                      Verified
                    </div>
                  )}
                  
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getKycStatusColor(profile.kycStatus)}`}>
                    KYC {profile.kycStatus}
                  </span>
                </div>
                
                <div className="flex flex-wrap gap-2 mb-3">
                  {profile.roles.map((role) => (
                    <span key={role} className="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                      {getRoleDisplayName(role)}
                    </span>
                  ))}
                </div>
                
                <p className="text-gray-600">Member since {new Date(profile.joinDate).toLocaleDateString()}</p>
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                {isEditing ? (
                  <input
                    type="email"
                    value={editedProfile.email}
                    onChange={(e) => setEditedProfile(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="flex items-center text-gray-900">
                    <Mail className="w-4 h-4 mr-2 text-gray-400" />
                    {profile.email}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                {isEditing ? (
                  <input
                    type="tel"
                    value={editedProfile.phone}
                    onChange={(e) => setEditedProfile(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="flex items-center text-gray-900">
                    <Phone className="w-4 h-4 mr-2 text-gray-400" />
                    {profile.phone}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                {isEditing ? (
                  <input
                    type="text"
                    value={editedProfile.location}
                    onChange={(e) => setEditedProfile(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="flex items-center text-gray-900">
                    <MapPin className="w-4 h-4 mr-2 text-gray-400" />
                    {profile.location}
                  </div>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                {isEditing ? (
                  <input
                    type="url"
                    value={editedProfile.website}
                    onChange={(e) => setEditedProfile(prev => ({ ...prev, website: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                ) : (
                  <div className="flex items-center text-gray-900">
                    <Globe className="w-4 h-4 mr-2 text-gray-400" />
                    <a href={profile.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                      {profile.website}
                    </a>
                  </div>
                )}
              </div>
            </div>

            {/* Bio */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
              {isEditing ? (
                <textarea
                  value={editedProfile.bio}
                  onChange={(e) => setEditedProfile(prev => ({ ...prev, bio: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              ) : (
                <p className="text-gray-700">{profile.bio}</p>
              )}
            </div>
          </div>
        </div>

        {/* Stats and Achievements */}
        <div className="space-y-6">
          {/* Portfolio Stats */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Portfolio Overview</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <DollarSign className="w-5 h-5 text-green-600 mr-2" />
                  <span className="text-gray-600">Total Value</span>
                </div>
                <span className="font-semibold text-gray-900">${userStats.portfolioValue.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <TrendingUp className="w-5 h-5 text-blue-600 mr-2" />
                  <span className="text-gray-600">Monthly Income</span>
                </div>
                <span className="font-semibold text-gray-900">${userStats.monthlyIncome.toLocaleString()}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Building2 className="w-5 h-5 text-purple-600 mr-2" />
                  <span className="text-gray-600">Properties</span>
                </div>
                <span className="font-semibold text-gray-900">{userStats.propertiesOwned}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Coins className="w-5 h-5 text-orange-600 mr-2" />
                  <span className="text-gray-600">Reward Points</span>
                </div>
                <span className="font-semibold text-gray-900">{userStats.rewardPoints.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Membership Level */}
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl shadow-sm p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Membership Level</h3>
                <p className="text-2xl font-bold">{userStats.membershipLevel}</p>
              </div>
              <Award className="w-12 h-12 opacity-80" />
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm mb-1">
                <span>Progress to Platinum</span>
                <span>75%</span>
              </div>
              <div className="w-full bg-white bg-opacity-30 rounded-full h-2">
                <div className="bg-white h-2 rounded-full" style={{ width: '75%' }}></div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button className="w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center">
                  <Wallet className="w-5 h-5 text-blue-600 mr-3" />
                  <span className="font-medium">Connect Wallet</span>
                </div>
              </button>
              
              <button className="w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center">
                  <Shield className="w-5 h-5 text-green-600 mr-3" />
                  <span className="font-medium">Security Settings</span>
                </div>
              </button>
              
              <button className="w-full text-left px-4 py-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center">
                  <Award className="w-5 h-5 text-purple-600 mr-3" />
                  <span className="font-medium">View Achievements</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
