'use client';

import { useState } from 'react';
import { 
  Building2,
  MapPin,
  DollarSign,
  Users,
  Calendar,
  TrendingUp,
  Eye,
  Edit,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Home,
  Bed,
  Bath,
  Square,
  Star,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface Property {
  id: string;
  title: string;
  address: string;
  type: 'apartment' | 'house' | 'condo' | 'townhouse';
  bedrooms: number;
  bathrooms: number;
  sqft: number;
  monthlyRent: number;
  currentTenant?: string;
  tenantSince?: string;
  occupancyStatus: 'occupied' | 'vacant' | 'maintenance';
  monthlyIncome: number;
  totalValue: number;
  roi: number;
  imageUrl: string;
  lastUpdated: string;
}

export default function PropertiesPanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'apartment' | 'house' | 'condo' | 'townhouse'>('all');
  const [filterStatus, setFilterStatus] = useState<'all' | 'occupied' | 'vacant' | 'maintenance'>('all');

  // Mock properties data
  const [properties] = useState<Property[]>([
    {
      id: '1',
      title: 'Modern Downtown Apartment',
      address: '123 Main St, Downtown, NY 10001',
      type: 'apartment',
      bedrooms: 2,
      bathrooms: 2,
      sqft: 1200,
      monthlyRent: 2800,
      currentTenant: 'John Smith',
      tenantSince: '2023-06-01',
      occupancyStatus: 'occupied',
      monthlyIncome: 2800,
      totalValue: 450000,
      roi: 7.5,
      imageUrl: '/api/placeholder/300/200',
      lastUpdated: '2025-01-20T10:30:00Z',
    },
    {
      id: '2',
      title: 'Suburban Family House',
      address: '456 Oak Ave, Suburbia, NY 10002',
      type: 'house',
      bedrooms: 4,
      bathrooms: 3,
      sqft: 2400,
      monthlyRent: 3500,
      occupancyStatus: 'vacant',
      monthlyIncome: 0,
      totalValue: 650000,
      roi: 6.5,
      imageUrl: '/api/placeholder/300/200',
      lastUpdated: '2025-01-18T14:15:00Z',
    },
    {
      id: '3',
      title: 'Luxury Condo with View',
      address: '789 Park Blvd, Uptown, NY 10003',
      type: 'condo',
      bedrooms: 3,
      bathrooms: 2,
      sqft: 1800,
      monthlyRent: 4200,
      currentTenant: 'Sarah Johnson',
      tenantSince: '2023-09-15',
      occupancyStatus: 'occupied',
      monthlyIncome: 4200,
      totalValue: 750000,
      roi: 6.7,
      imageUrl: '/api/placeholder/300/200',
      lastUpdated: '2025-01-22T09:45:00Z',
    },
    {
      id: '4',
      title: 'Cozy Townhouse',
      address: '321 Elm St, Midtown, NY 10004',
      type: 'townhouse',
      bedrooms: 3,
      bathrooms: 2,
      sqft: 1600,
      monthlyRent: 3200,
      occupancyStatus: 'maintenance',
      monthlyIncome: 0,
      totalValue: 520000,
      roi: 7.4,
      imageUrl: '/api/placeholder/300/200',
      lastUpdated: '2025-01-19T16:20:00Z',
    },
  ]);

  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.address.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || property.type === filterType;
    const matchesStatus = filterStatus === 'all' || property.occupancyStatus === filterStatus;
    
    return matchesSearch && matchesType && matchesStatus;
  });

  const totalProperties = properties.length;
  const occupiedProperties = properties.filter(p => p.occupancyStatus === 'occupied').length;
  const vacantProperties = properties.filter(p => p.occupancyStatus === 'vacant').length;
  const totalMonthlyIncome = properties.reduce((sum, p) => sum + p.monthlyIncome, 0);
  const totalValue = properties.reduce((sum, p) => sum + p.totalValue, 0);
  const averageROI = properties.reduce((sum, p) => sum + p.roi, 0) / properties.length;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'occupied':
        return 'bg-green-100 text-green-800';
      case 'vacant':
        return 'bg-yellow-100 text-yellow-800';
      case 'maintenance':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'occupied':
        return <CheckCircle className="w-4 h-4" />;
      case 'vacant':
        return <Home className="w-4 h-4" />;
      case 'maintenance':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Home className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">My Properties</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Add Property
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Building2 className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Properties</p>
              <p className="text-2xl font-bold text-gray-900">{totalProperties}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Monthly Income</p>
              <p className="text-2xl font-bold text-gray-900">${totalMonthlyIncome.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Average ROI</p>
              <p className="text-2xl font-bold text-gray-900">{averageROI.toFixed(1)}%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Occupancy Rate</p>
              <p className="text-2xl font-bold text-gray-900">{Math.round((occupiedProperties / totalProperties) * 100)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="mobile-input-icon" />
              <input
                type="text"
                placeholder="Search properties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="mobile-input-with-icon w-full"
              />
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Types</option>
              <option value="apartment">Apartment</option>
              <option value="house">House</option>
              <option value="condo">Condo</option>
              <option value="townhouse">Townhouse</option>
            </select>

            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="occupied">Occupied</option>
              <option value="vacant">Vacant</option>
              <option value="maintenance">Maintenance</option>
            </select>
          </div>
        </div>
      </div>

      {/* Properties Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredProperties.map((property) => (
          <div key={property.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            <div className="relative">
              <img
                src={property.imageUrl}
                alt={property.title}
                className="w-full h-48 object-cover"
              />
              <div className="absolute top-4 left-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(property.occupancyStatus)}`}>
                  {getStatusIcon(property.occupancyStatus)}
                  <span className="ml-1 capitalize">{property.occupancyStatus}</span>
                </span>
              </div>
              <div className="absolute top-4 right-4">
                <button className="bg-white bg-opacity-90 p-2 rounded-full hover:bg-opacity-100 transition-all">
                  <MoreVertical className="w-4 h-4 text-gray-600" />
                </button>
              </div>
            </div>

            <div className="p-6">
              <div className="flex items-start justify-between mb-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{property.title}</h3>
                  <p className="text-sm text-gray-600 flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {property.address}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900">${property.monthlyRent.toLocaleString()}/mo</p>
                  <p className="text-sm text-gray-600">ROI: {property.roi}%</p>
                </div>
              </div>

              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                <span className="flex items-center">
                  <Bed className="w-4 h-4 mr-1" />
                  {property.bedrooms} bed
                </span>
                <span className="flex items-center">
                  <Bath className="w-4 h-4 mr-1" />
                  {property.bathrooms} bath
                </span>
                <span className="flex items-center">
                  <Square className="w-4 h-4 mr-1" />
                  {property.sqft.toLocaleString()} sqft
                </span>
              </div>

              {property.currentTenant && (
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <p className="text-sm font-medium text-gray-900">Current Tenant</p>
                  <p className="text-sm text-gray-600">{property.currentTenant}</p>
                  <p className="text-xs text-gray-500">Since {new Date(property.tenantSince!).toLocaleDateString()}</p>
                </div>
              )}

              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <button className="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                    <Edit className="w-4 h-4" />
                  </button>
                </div>
                <p className="text-xs text-gray-500">
                  Updated {new Date(property.lastUpdated).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredProperties.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterType !== 'all' || filterStatus !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'Get started by adding your first property.'
            }
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Add Property
          </button>
        </div>
      )}
    </div>
  );
}
