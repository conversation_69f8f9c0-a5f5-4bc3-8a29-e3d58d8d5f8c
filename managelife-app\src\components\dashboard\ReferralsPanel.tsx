'use client';

import { useState } from 'react';
import { 
  UserPlus,
  Gift,
  DollarSign,
  Share2,
  Copy,
  Mail,
  MessageSquare,
  Users,
  TrendingUp,
  CheckCircle,
  Clock,
  Star,
  Award,
  Link,
  QrCode,
  Facebook,
  Twitter,
  Instagram,
  Send,
  Target,
  Zap
} from 'lucide-react';

interface Referral {
  id: string;
  referredUser: {
    name: string;
    email: string;
    avatar?: string;
  };
  status: 'pending' | 'completed' | 'rewarded';
  referralDate: string;
  completionDate?: string;
  reward: {
    amount: number;
    type: 'cash' | 'credit' | 'token';
    status: 'pending' | 'paid' | 'processing';
  };
  activity: string;
}

interface ReferralReward {
  id: string;
  title: string;
  description: string;
  amount: number;
  type: 'cash' | 'credit' | 'token';
  requirement: string;
  icon: string;
  isActive: boolean;
}

export default function ReferralsPanel() {
  const [activeTab, setActiveTab] = useState<'overview' | 'invite' | 'history' | 'rewards'>('overview');
  const [referralCode] = useState('MLIFE-ABC123');
  const [referralLink] = useState('https://managelife.com/join?ref=MLIFE-ABC123');

  // Mock referrals data
  const [referrals] = useState<Referral[]>([
    {
      id: '1',
      referredUser: {
        name: '<PERSON>',
        email: '<EMAIL>',
        avatar: '/api/placeholder/150/150'
      },
      status: 'rewarded',
      referralDate: '2025-01-15T10:00:00Z',
      completionDate: '2025-01-20T14:30:00Z',
      reward: {
        amount: 50,
        type: 'cash',
        status: 'paid'
      },
      activity: 'Completed first property investment'
    },
    {
      id: '2',
      referredUser: {
        name: 'Sarah Johnson',
        email: '<EMAIL>'
      },
      status: 'completed',
      referralDate: '2025-01-20T16:45:00Z',
      completionDate: '2025-01-25T09:15:00Z',
      reward: {
        amount: 25,
        type: 'credit',
        status: 'processing'
      },
      activity: 'Signed up and verified account'
    },
    {
      id: '3',
      referredUser: {
        name: 'Mike Chen',
        email: '<EMAIL>'
      },
      status: 'pending',
      referralDate: '2025-01-22T12:20:00Z',
      reward: {
        amount: 25,
        type: 'credit',
        status: 'pending'
      },
      activity: 'Account created, pending verification'
    }
  ]);

  // Mock rewards data
  const [rewards] = useState<ReferralReward[]>([
    {
      id: '1',
      title: 'Friend Sign-up Bonus',
      description: 'Earn $25 when your friend signs up and verifies their account',
      amount: 25,
      type: 'cash',
      requirement: 'Friend completes account verification',
      icon: 'user-plus',
      isActive: true
    },
    {
      id: '2',
      title: 'First Investment Bonus',
      description: 'Earn $50 when your friend makes their first property investment',
      amount: 50,
      type: 'cash',
      requirement: 'Friend invests in their first property',
      icon: 'building',
      isActive: true
    },
    {
      id: '3',
      title: 'Token Reward',
      description: 'Earn 100 $MLife tokens for each successful referral',
      amount: 100,
      type: 'token',
      requirement: 'Friend completes onboarding process',
      icon: 'coins',
      isActive: true
    },
    {
      id: '4',
      title: 'VIP Referral Bonus',
      description: 'Special bonus for referring premium members',
      amount: 100,
      type: 'cash',
      requirement: 'Friend upgrades to premium membership',
      icon: 'star',
      isActive: false
    }
  ]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'rewarded':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRewardStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'paid':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const totalReferrals = referrals.length;
  const completedReferrals = referrals.filter(r => r.status === 'completed' || r.status === 'rewarded').length;
  const totalEarnings = referrals
    .filter(r => r.status === 'rewarded')
    .reduce((sum, r) => sum + r.reward.amount, 0);
  const pendingEarnings = referrals
    .filter(r => r.reward.status === 'processing')
    .reduce((sum, r) => sum + r.reward.amount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Referral Program</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Share2 className="w-4 h-4 mr-2" />
          Invite Friends
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Referrals</p>
              <p className="text-2xl font-bold text-gray-900">{totalReferrals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Successful</p>
              <p className="text-2xl font-bold text-gray-900">{completedReferrals}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Earned</p>
              <p className="text-2xl font-bold text-gray-900">${totalEarnings}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-gray-900">${pendingEarnings}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: TrendingUp },
              { id: 'invite', label: 'Invite Friends', icon: UserPlus },
              { id: 'history', label: 'Referral History', icon: Clock },
              { id: 'rewards', label: 'Rewards', icon: Gift },
            ].map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <IconComponent className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Referral Performance</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Conversion Rate</span>
                      <span className="font-semibold">{completedReferrals > 0 ? Math.round((completedReferrals / totalReferrals) * 100) : 0}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average Reward</span>
                      <span className="font-semibold">${completedReferrals > 0 ? Math.round(totalEarnings / completedReferrals) : 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">This Month</span>
                      <span className="font-semibold text-green-600">+{referrals.filter(r => new Date(r.referralDate).getMonth() === new Date().getMonth()).length} referrals</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-6 border border-green-200">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                  <div className="space-y-3">
                    <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                      <Share2 className="w-4 h-4 mr-2" />
                      Share Referral Link
                    </button>
                    <button className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center">
                      <Copy className="w-4 h-4 mr-2" />
                      Copy Referral Code
                    </button>
                    <button className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors flex items-center justify-center">
                      <QrCode className="w-4 h-4 mr-2" />
                      Generate QR Code
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Referrals</h3>
                <div className="space-y-4">
                  {referrals.slice(0, 3).map((referral) => (
                    <div key={referral.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        {referral.referredUser.avatar ? (
                          <img
                            src={referral.referredUser.avatar}
                            alt={referral.referredUser.name}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                            <UserPlus className="w-5 h-5 text-gray-400" />
                          </div>
                        )}
                        <div>
                          <p className="font-medium text-gray-900">{referral.referredUser.name}</p>
                          <p className="text-sm text-gray-600">{referral.activity}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(referral.status)}`}>
                          {referral.status}
                        </span>
                        <p className="text-sm text-gray-600 mt-1">${referral.reward.amount}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Invite Tab */}
          {activeTab === 'invite' && (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Invite Friends & Earn Rewards</h3>
                <p className="text-gray-600">Share ManageLife with your friends and earn rewards when they join!</p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Your Referral Code</h4>
                  <div className="flex items-center space-x-2 mb-4">
                    <input
                      type="text"
                      value={referralCode}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                    />
                    <button
                      onClick={() => copyToClipboard(referralCode)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>

                  <h4 className="font-semibold text-gray-900 mb-4">Your Referral Link</h4>
                  <div className="flex items-center space-x-2 mb-6">
                    <input
                      type="text"
                      value={referralLink}
                      readOnly
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm"
                    />
                    <button
                      onClick={() => copyToClipboard(referralLink)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                  </div>

                  <div className="flex space-x-2">
                    <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                      <Mail className="w-4 h-4 mr-2" />
                      Email
                    </button>
                    <button className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center">
                      <MessageSquare className="w-4 h-4 mr-2" />
                      SMS
                    </button>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Share on Social Media</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <button className="bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center">
                      <Facebook className="w-5 h-5 mr-2" />
                      Facebook
                    </button>
                    <button className="bg-blue-400 text-white py-3 px-4 rounded-lg hover:bg-blue-500 transition-colors flex items-center justify-center">
                      <Twitter className="w-5 h-5 mr-2" />
                      Twitter
                    </button>
                    <button className="bg-pink-600 text-white py-3 px-4 rounded-lg hover:bg-pink-700 transition-colors flex items-center justify-center">
                      <Instagram className="w-5 h-5 mr-2" />
                      Instagram
                    </button>
                    <button className="bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center">
                      <Link className="w-5 h-5 mr-2" />
                      Copy Link
                    </button>
                  </div>

                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h5 className="font-medium text-blue-900 mb-2">Pro Tip!</h5>
                    <p className="text-sm text-blue-800">
                      Personal messages work best! Tell your friends why you love ManageLife and how it's helped you.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* History Tab */}
          {activeTab === 'history' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Referral History</h3>
              {referrals.map((referral) => (
                <div key={referral.id} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4">
                      {referral.referredUser.avatar ? (
                        <img
                          src={referral.referredUser.avatar}
                          alt={referral.referredUser.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserPlus className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                      <div>
                        <h4 className="font-semibold text-gray-900">{referral.referredUser.name}</h4>
                        <p className="text-sm text-gray-600">{referral.referredUser.email}</p>
                        <p className="text-sm text-gray-600 mt-1">{referral.activity}</p>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>Referred: {formatDate(referral.referralDate)}</span>
                          {referral.completionDate && (
                            <span>Completed: {formatDate(referral.completionDate)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(referral.status)}`}>
                        {referral.status}
                      </span>
                      <div className="mt-2">
                        <p className="font-semibold text-gray-900">${referral.reward.amount}</p>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRewardStatusColor(referral.reward.status)}`}>
                          {referral.reward.status}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Rewards Tab */}
          {activeTab === 'rewards' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Available Rewards</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {rewards.map((reward) => (
                  <div key={reward.id} className={`border rounded-lg p-6 ${reward.isActive ? 'border-blue-200 bg-blue-50' : 'border-gray-200 bg-gray-50'}`}>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${reward.isActive ? 'bg-blue-100' : 'bg-gray-200'}`}>
                          {reward.icon === 'user-plus' && <UserPlus className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}
                          {reward.icon === 'building' && <Target className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}
                          {reward.icon === 'coins' && <Zap className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}
                          {reward.icon === 'star' && <Star className={`w-6 h-6 ${reward.isActive ? 'text-blue-600' : 'text-gray-400'}`} />}
                        </div>
                        <div>
                          <h4 className={`font-semibold ${reward.isActive ? 'text-gray-900' : 'text-gray-500'}`}>{reward.title}</h4>
                          <p className={`text-sm ${reward.isActive ? 'text-gray-600' : 'text-gray-400'}`}>{reward.description}</p>
                        </div>
                      </div>
                      {!reward.isActive && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                          Coming Soon
                        </span>
                      )}
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className={`text-sm ${reward.isActive ? 'text-gray-600' : 'text-gray-400'}`}>Reward Amount</span>
                        <span className={`font-semibold ${reward.isActive ? 'text-gray-900' : 'text-gray-500'}`}>
                          {reward.type === 'token' ? `${reward.amount} $MLife` : `$${reward.amount}`}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className={`text-sm ${reward.isActive ? 'text-gray-600' : 'text-gray-400'}`}>Requirement</span>
                        <span className={`text-sm ${reward.isActive ? 'text-gray-900' : 'text-gray-500'}`}>{reward.requirement}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
