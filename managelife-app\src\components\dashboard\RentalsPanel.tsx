'use client';

import { useState } from 'react';
import { 
  Home,
  Calendar,
  DollarSign,
  User,
  Phone,
  Mail,
  MapPin,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Filter,
  Search,
  Plus,
  Eye,
  Edit,
  MessageSquare,
  FileText,
  Wrench
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface Rental {
  id: string;
  propertyTitle: string;
  propertyAddress: string;
  tenantName: string;
  tenantEmail: string;
  tenantPhone: string;
  rentAmount: number;
  leaseStart: string;
  leaseEnd: string;
  status: 'active' | 'pending' | 'expired' | 'terminated';
  paymentStatus: 'paid' | 'pending' | 'overdue';
  lastPayment: string;
  nextPayment: string;
  securityDeposit: number;
  propertyImage: string;
}

interface MaintenanceRequest {
  id: string;
  propertyTitle: string;
  tenantName: string;
  issue: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in-progress' | 'completed';
  dateSubmitted: string;
  description: string;
}

export default function RentalsPanel() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<'rentals' | 'maintenance' | 'applications'>('rentals');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'pending' | 'expired'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock rental data
  const rentals: Rental[] = [
    {
      id: '1',
      propertyTitle: 'Luxury Downtown Apartment',
      propertyAddress: '123 Main St, Manhattan, NY',
      tenantName: 'Sarah Johnson',
      tenantEmail: '<EMAIL>',
      tenantPhone: '+****************',
      rentAmount: 6500,
      leaseStart: '2023-01-01',
      leaseEnd: '2025-12-31',
      status: 'active',
      paymentStatus: 'paid',
      lastPayment: '2025-01-01',
      nextPayment: '2025-02-01',
      securityDeposit: 13000,
      propertyImage: '/api/placeholder/300/200',
    },
    {
      id: '2',
      propertyTitle: 'Suburban Family Home',
      propertyAddress: '456 Oak Ave, Phoenix, AZ',
      tenantName: 'Michael Chen',
      tenantEmail: '<EMAIL>',
      tenantPhone: '+****************',
      rentAmount: 2800,
      leaseStart: '2023-07-01',
      leaseEnd: '2025-06-30',
      status: 'active',
      paymentStatus: 'pending',
      lastPayment: '2023-12-01',
      nextPayment: '2025-01-01',
      securityDeposit: 5600,
      propertyImage: '/api/placeholder/300/200',
    },
    {
      id: '3',
      propertyTitle: 'Modern Office Space',
      propertyAddress: '789 Business Blvd, Austin, TX',
      tenantName: 'TechStart Inc.',
      tenantEmail: '<EMAIL>',
      tenantPhone: '+****************',
      rentAmount: 8200,
      leaseStart: '2022-11-01',
      leaseEnd: '2025-10-31',
      status: 'active',
      paymentStatus: 'overdue',
      lastPayment: '2023-11-01',
      nextPayment: '2023-12-01',
      securityDeposit: 16400,
      propertyImage: '/api/placeholder/300/200',
    },
  ];

  const maintenanceRequests: MaintenanceRequest[] = [
    {
      id: '1',
      propertyTitle: 'Luxury Downtown Apartment',
      tenantName: 'Sarah Johnson',
      issue: 'Leaking faucet in kitchen',
      priority: 'medium',
      status: 'open',
      dateSubmitted: '2025-01-15',
      description: 'The kitchen faucet has been dripping constantly for the past week.',
    },
    {
      id: '2',
      propertyTitle: 'Suburban Family Home',
      tenantName: 'Michael Chen',
      issue: 'Heating system not working',
      priority: 'urgent',
      status: 'in-progress',
      dateSubmitted: '2025-01-10',
      description: 'The heating system stopped working completely. House is very cold.',
    },
  ];

  const filteredRentals = rentals.filter(rental => {
    const matchesStatus = filterStatus === 'all' || rental.status === filterStatus;
    const matchesSearch = rental.propertyTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rental.tenantName.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'expired': return 'bg-red-100 text-red-800';
      case 'terminated': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-blue-100 text-blue-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Rentals</p>
              <p className="text-2xl font-bold text-gray-900">{rentals.filter(r => r.status === 'active').length}</p>
            </div>
            <Home className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
              <p className="text-2xl font-bold text-gray-900">
                ${rentals.filter(r => r.status === 'active').reduce((sum, r) => sum + r.rentAmount, 0).toLocaleString()}
              </p>
            </div>
            <DollarSign className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overdue Payments</p>
              <p className="text-2xl font-bold text-gray-900">{rentals.filter(r => r.paymentStatus === 'overdue').length}</p>
            </div>
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Maintenance Requests</p>
              <p className="text-2xl font-bold text-gray-900">{maintenanceRequests.filter(r => r.status !== 'completed').length}</p>
            </div>
            <Wrench className="w-8 h-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'rentals', label: 'Active Rentals', icon: Home },
              { id: 'maintenance', label: 'Maintenance', icon: Wrench },
              { id: 'applications', label: 'Applications', icon: FileText },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'rentals' && (
            <div className="space-y-6">
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search properties or tenants..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="pending">Pending</option>
                  <option value="expired">Expired</option>
                </select>
              </div>

              {/* Rentals List */}
              <div className="space-y-4">
                {filteredRentals.map((rental) => (
                  <div key={rental.id} className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start space-x-4">
                        <img
                          src={rental.propertyImage}
                          alt={rental.propertyTitle}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{rental.propertyTitle}</h3>
                          <div className="flex items-center text-gray-600 mt-1">
                            <MapPin className="w-4 h-4 mr-1" />
                            <span className="text-sm">{rental.propertyAddress}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(rental.status)}`}>
                          {rental.status}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPaymentStatusColor(rental.paymentStatus)}`}>
                          {rental.paymentStatus}
                        </span>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-3 gap-6">
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Tenant Information</h4>
                        <div className="space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <User className="w-4 h-4 mr-2" />
                            {rental.tenantName}
                          </div>
                          <div className="flex items-center">
                            <Mail className="w-4 h-4 mr-2" />
                            {rental.tenantEmail}
                          </div>
                          <div className="flex items-center">
                            <Phone className="w-4 h-4 mr-2" />
                            {rental.tenantPhone}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Lease Details</h4>
                        <div className="space-y-1 text-sm text-gray-600">
                          <div>Rent: <span className="font-medium text-green-600">${rental.rentAmount.toLocaleString()}/month</span></div>
                          <div>Lease: {new Date(rental.leaseStart).toLocaleDateString()} - {new Date(rental.leaseEnd).toLocaleDateString()}</div>
                          <div>Security Deposit: ${rental.securityDeposit.toLocaleString()}</div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Payment Status</h4>
                        <div className="space-y-1 text-sm text-gray-600">
                          <div>Last Payment: {new Date(rental.lastPayment).toLocaleDateString()}</div>
                          <div>Next Payment: {new Date(rental.nextPayment).toLocaleDateString()}</div>
                        </div>
                        <div className="flex space-x-2 mt-3">
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View Details
                          </button>
                          <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                            Contact Tenant
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'maintenance' && (
            <div className="space-y-4">
              {maintenanceRequests.map((request) => (
                <div key={request.id} className="bg-gray-50 rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{request.issue}</h3>
                      <p className="text-gray-600">{request.propertyTitle} • {request.tenantName}</p>
                    </div>
                    <div className="flex space-x-2">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(request.priority)}`}>
                        {request.priority}
                      </span>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        request.status === 'completed' ? 'bg-green-100 text-green-800' :
                        request.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {request.status}
                      </span>
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">{request.description}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      Submitted: {new Date(request.dateSubmitted).toLocaleDateString()}
                    </span>
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                        Update Status
                      </button>
                      <button className="text-green-600 hover:text-green-800 text-sm font-medium">
                        Contact Tenant
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'applications' && (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Rental Applications</h3>
              <p className="text-gray-600">No pending applications at this time.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
