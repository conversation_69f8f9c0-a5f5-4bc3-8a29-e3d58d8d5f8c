'use client';

import { useState, useEffect } from 'react';
import {
  User,
  Shield,
  Bell,
  Globe,
  Palette,
  Database,
  Key,
  Smartphone,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Save,
  X,
  Check,
  AlertTriangle,
  Trash2,
  Download,
  Upload,
  Settings
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface SettingsData {
  // Profile Settings
  displayName: string;
  bio: string;
  location: string;
  website: string;
  
  // Privacy Settings
  profileVisibility: 'public' | 'private' | 'friends';
  showEmail: boolean;
  showWallet: boolean;
  showActivity: boolean;
  
  // Notification Settings
  emailNotifications: boolean;
  pushNotifications: boolean;
  rewardNotifications: boolean;
  communityNotifications: boolean;
  marketingEmails: boolean;
  
  // App Preferences
  language: string;
  currency: string;
  timezone: string;
  theme: string;
  
  // Security Settings
  twoFactorEnabled: boolean;
  loginAlerts: boolean;
  sessionTimeout: number;
}

export default function SettingsPanel() {
  const { user } = useAuth();
  const [activeSection, setActiveSection] = useState<'profile' | 'privacy' | 'notifications' | 'preferences' | 'security' | 'data'>('profile');
  const [isSaving, setIsSaving] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const [settings, setSettings] = useState<SettingsData>({
    displayName: user?.name || '',
    bio: '',
    location: '',
    website: '',
    profileVisibility: 'public',
    showEmail: false,
    showWallet: false,
    showActivity: true,
    emailNotifications: true,
    pushNotifications: true,
    rewardNotifications: true,
    communityNotifications: false,
    marketingEmails: false,
    language: 'en',
    currency: 'usd',
    timezone: 'UTC',
    theme: 'light',
    twoFactorEnabled: false,
    loginAlerts: true,
    sessionTimeout: 30,
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleSaveSettings = async () => {
    setIsSaving(true);
    setSaveMessage(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSaveMessage({ type: 'success', text: 'Settings saved successfully!' });
    } catch (error: any) {
      setSaveMessage({ type: 'error', text: error.message || 'Failed to save settings' });
    } finally {
      setIsSaving(false);
    }
  };

  const sections = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'preferences', label: 'Preferences', icon: Globe },
    { id: 'security', label: 'Security', icon: Lock },
    { id: 'data', label: 'Data & Privacy', icon: Database },
  ];

  return (
    <div className="mobile-card">
      <div className="border-b border-gray-200">
        {/* Mobile Tab Navigation */}
        <div className="sm:hidden">
          <div className="mobile-container py-3">
            <select
              value={activeSection}
              onChange={(e) => setActiveSection(e.target.value as any)}
              className="mobile-input w-full"
            >
              {sections.map((section) => (
                <option key={section.id} value={section.id}>
                  {section.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Desktop Tab Navigation */}
        <div className="hidden sm:block">
          <div className="mobile-nav-scroll-container overflow-x-auto">
            <div className="flex space-x-4 lg:space-x-8 px-4 sm:px-6 min-w-max">
              {sections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id as any)}
                    className={`touch-target py-3 sm:py-4 px-1 border-b-2 font-medium text-sm flex items-center whitespace-nowrap ${
                      activeSection === section.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4 mr-2" />
                    <span className="hidden md:inline">{section.label}</span>
                    <span className="md:hidden">{section.label.split(' ')[0]}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      <div className="mobile-container py-4 sm:py-6">
        {/* Save Message */}
        {saveMessage && (
          <div className={`mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg flex items-start ${
            saveMessage.type === 'success'
              ? 'bg-green-50 border border-green-200 text-green-700'
              : 'bg-red-50 border border-red-200 text-red-700'
          }`}>
            {saveMessage.type === 'success' ? (
              <Check className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
            ) : (
              <AlertTriangle className="w-5 h-5 mr-3 flex-shrink-0 mt-0.5" />
            )}
            <p className="mobile-text-responsive flex-1">{saveMessage.text}</p>
            <button
              onClick={() => setSaveMessage(null)}
              className="touch-target ml-2 text-gray-400 hover:text-gray-600 p-1 rounded-md"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Profile Settings */}
        {activeSection === 'profile' && (
          <div className="mobile-space-y-6">
            <h3 className="text-base sm:text-lg font-semibold text-gray-900">Profile Information</h3>

            <div className="mobile-grid">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Display Name
                </label>
                <input
                  type="text"
                  value={settings.displayName}
                  onChange={(e) => setSettings(prev => ({ ...prev, displayName: e.target.value }))}
                  className="mobile-input w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Location
                </label>
                <input
                  type="text"
                  value={settings.location}
                  onChange={(e) => setSettings(prev => ({ ...prev, location: e.target.value }))}
                  className="mobile-input w-full"
                  placeholder="City, Country"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bio
              </label>
              <textarea
                value={settings.bio}
                onChange={(e) => setSettings(prev => ({ ...prev, bio: e.target.value }))}
                rows={3}
                className="mobile-input w-full resize-none"
                placeholder="Tell us about yourself..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Website
              </label>
              <input
                type="url"
                value={settings.website}
                onChange={(e) => setSettings(prev => ({ ...prev, website: e.target.value }))}
                className="mobile-input w-full"
                placeholder="https://yourwebsite.com"
              />
            </div>
          </div>
        )}

        {/* Notifications Settings */}
        {activeSection === 'notifications' && (
          <div className="mobile-space-y-6">
            <h3 className="text-base sm:text-lg font-semibold text-gray-900">Notification Preferences</h3>

            <div className="mobile-space-y-4">
              {[
                { key: 'emailNotifications', label: 'Email Notifications', description: 'Receive notifications via email' },
                { key: 'pushNotifications', label: 'Push Notifications', description: 'Receive push notifications in your browser' },
                { key: 'rewardNotifications', label: 'Reward Notifications', description: 'Get notified when you earn rewards' },
                { key: 'communityNotifications', label: 'Community Updates', description: 'Updates from the ManageLife community' },
                { key: 'marketingEmails', label: 'Marketing Emails', description: 'Promotional emails and product updates' },
              ].map((item) => (
                <div key={item.key} className="flex items-start sm:items-center justify-between py-3 sm:py-4 border-b border-gray-100 last:border-b-0 gap-3">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 text-sm sm:text-base">{item.label}</h4>
                    <p className="text-xs sm:text-sm text-gray-600 mobile-text-responsive mt-1">{item.description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer touch-target flex-shrink-0">
                    <input
                      type="checkbox"
                      checked={settings[item.key as keyof SettingsData] as boolean}
                      onChange={(e) => setSettings(prev => ({ ...prev, [item.key]: e.target.checked }))}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Other sections would go here... */}
        {activeSection !== 'profile' && activeSection !== 'notifications' && (
          <div className="text-center py-8 sm:py-12">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Settings className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" />
            </div>
            <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-2">
              {sections.find(s => s.id === activeSection)?.label} Settings
            </h3>
            <p className="text-gray-600 mobile-text-responsive">This section is coming soon.</p>
          </div>
        )}

        {/* Save Button */}
        {(activeSection === 'profile' || activeSection === 'notifications') && (
          <div className="mt-6 sm:mt-8 pt-4 sm:pt-6 border-t border-gray-200">
            <button
              onClick={handleSaveSettings}
              disabled={isSaving}
              className="mobile-btn bg-blue-600 text-white hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isSaving ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
