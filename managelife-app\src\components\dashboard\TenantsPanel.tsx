'use client';

import { useState } from 'react';
import { 
  Users,
  User,
  Phone,
  Mail,
  MapPin,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  MessageSquare,
  Plus,
  Search,
  Filter,
  Eye,
  Edit,
  Star,
  TrendingUp,
  Home,
  CreditCard,
  Shield
} from 'lucide-react';

interface Tenant {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  propertyId: string;
  propertyAddress: string;
  leaseStart: string;
  leaseEnd: string;
  monthlyRent: number;
  securityDeposit: number;
  paymentStatus: 'current' | 'late' | 'overdue';
  lastPayment: string;
  nextPayment: string;
  leaseStatus: 'active' | 'expiring' | 'expired' | 'terminated';
  rating: number;
  notes?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  maintenanceRequests: number;
  paymentHistory: {
    onTime: number;
    late: number;
    total: number;
  };
}

export default function TenantsPanel() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'current' | 'late' | 'overdue'>('all');
  const [filterLease, setFilterLease] = useState<'all' | 'active' | 'expiring' | 'expired'>('all');
  const [sortBy, setSortBy] = useState<'name' | 'property' | 'rent' | 'lease-end'>('name');

  // Mock tenants data
  const [tenants] = useState<Tenant[]>([
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '(*************',
      avatar: '/api/placeholder/150/150',
      propertyId: 'prop-1',
      propertyAddress: '123 Main St, Downtown, NY 10001',
      leaseStart: '2023-06-01T00:00:00Z',
      leaseEnd: '2025-05-31T23:59:59Z',
      monthlyRent: 2800,
      securityDeposit: 2800,
      paymentStatus: 'current',
      lastPayment: '2025-01-01T00:00:00Z',
      nextPayment: '2025-02-01T00:00:00Z',
      leaseStatus: 'expiring',
      rating: 5,
      notes: 'Excellent tenant, always pays on time. Very clean and respectful.',
      emergencyContact: {
        name: 'Jane Smith',
        phone: '(*************',
        relationship: 'Spouse'
      },
      maintenanceRequests: 2,
      paymentHistory: {
        onTime: 8,
        late: 0,
        total: 8
      }
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      propertyId: 'prop-2',
      propertyAddress: '789 Park Blvd, Uptown, NY 10003',
      leaseStart: '2023-09-15T00:00:00Z',
      leaseEnd: '2025-09-14T23:59:59Z',
      monthlyRent: 4200,
      securityDeposit: 4200,
      paymentStatus: 'current',
      lastPayment: '2025-01-15T00:00:00Z',
      nextPayment: '2025-02-15T00:00:00Z',
      leaseStatus: 'active',
      rating: 4,
      emergencyContact: {
        name: 'Mike Johnson',
        phone: '(*************',
        relationship: 'Brother'
      },
      maintenanceRequests: 1,
      paymentHistory: {
        onTime: 4,
        late: 0,
        total: 4
      }
    },
    {
      id: '3',
      name: 'Mike Chen',
      email: '<EMAIL>',
      phone: '(*************',
      propertyId: 'prop-3',
      propertyAddress: '321 Elm St, Midtown, NY 10004',
      leaseStart: '2023-03-01T00:00:00Z',
      leaseEnd: '2025-02-29T23:59:59Z',
      monthlyRent: 3200,
      securityDeposit: 3200,
      paymentStatus: 'late',
      lastPayment: '2023-12-28T00:00:00Z',
      nextPayment: '2025-01-01T00:00:00Z',
      leaseStatus: 'expiring',
      rating: 3,
      notes: 'Generally good tenant but occasionally late with payments.',
      emergencyContact: {
        name: 'Lisa Chen',
        phone: '(*************',
        relationship: 'Sister'
      },
      maintenanceRequests: 4,
      paymentHistory: {
        onTime: 8,
        late: 2,
        total: 10
      }
    },
    {
      id: '4',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      phone: '(*************',
      propertyId: 'prop-4',
      propertyAddress: '654 Oak Ave, Suburbia, NY 10002',
      leaseStart: '2023-12-01T00:00:00Z',
      leaseEnd: '2025-11-30T23:59:59Z',
      monthlyRent: 2500,
      securityDeposit: 2500,
      paymentStatus: 'overdue',
      lastPayment: '2023-11-28T00:00:00Z',
      nextPayment: '2023-12-01T00:00:00Z',
      leaseStatus: 'active',
      rating: 2,
      notes: 'Payment issues. Need to follow up regularly.',
      emergencyContact: {
        name: 'Carlos Rodriguez',
        phone: '(*************',
        relationship: 'Father'
      },
      maintenanceRequests: 6,
      paymentHistory: {
        onTime: 1,
        late: 1,
        total: 2
      }
    }
  ]);

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.propertyAddress.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || tenant.paymentStatus === filterStatus;
    const matchesLease = filterLease === 'all' || tenant.leaseStatus === filterLease;
    
    return matchesSearch && matchesStatus && matchesLease;
  });

  // Sort tenants
  const sortedTenants = [...filteredTenants].sort((a, b) => {
    switch (sortBy) {
      case 'name':
        return a.name.localeCompare(b.name);
      case 'property':
        return a.propertyAddress.localeCompare(b.propertyAddress);
      case 'rent':
        return b.monthlyRent - a.monthlyRent;
      case 'lease-end':
        return new Date(a.leaseEnd).getTime() - new Date(b.leaseEnd).getTime();
      default:
        return 0;
    }
  });

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'current':
        return 'bg-green-100 text-green-800';
      case 'late':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getLeaseStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-blue-100 text-blue-800';
      case 'expiring':
        return 'bg-orange-100 text-orange-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      case 'terminated':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case 'current':
        return <CheckCircle className="w-4 h-4" />;
      case 'late':
        return <Clock className="w-4 h-4" />;
      case 'overdue':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getDaysUntilLeaseEnd = (leaseEnd: string) => {
    const now = new Date();
    const end = new Date(leaseEnd);
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const totalTenants = tenants.length;
  const currentTenants = tenants.filter(t => t.paymentStatus === 'current').length;
  const latePayments = tenants.filter(t => t.paymentStatus === 'late' || t.paymentStatus === 'overdue').length;
  const expiringLeases = tenants.filter(t => {
    const daysUntilEnd = getDaysUntilLeaseEnd(t.leaseEnd);
    return daysUntilEnd <= 60 && daysUntilEnd > 0;
  }).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">Tenant Management</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Add Tenant
        </button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Tenants</p>
              <p className="text-2xl font-bold text-gray-900">{totalTenants}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Current Payments</p>
              <p className="text-2xl font-bold text-gray-900">{currentTenants}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Late Payments</p>
              <p className="text-2xl font-bold text-gray-900">{latePayments}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-6 h-6 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Expiring Leases</p>
              <p className="text-2xl font-bold text-gray-900">{expiringLeases}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search tenants..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Payment Status</option>
            <option value="current">Current</option>
            <option value="late">Late</option>
            <option value="overdue">Overdue</option>
          </select>

          <select
            value={filterLease}
            onChange={(e) => setFilterLease(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Lease Status</option>
            <option value="active">Active</option>
            <option value="expiring">Expiring</option>
            <option value="expired">Expired</option>
          </select>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="name">Sort by Name</option>
            <option value="property">Sort by Property</option>
            <option value="rent">Sort by Rent</option>
            <option value="lease-end">Sort by Lease End</option>
          </select>
        </div>
      </div>

      {/* Tenants List */}
      <div className="space-y-4">
        {sortedTenants.map((tenant) => {
          const daysUntilLeaseEnd = getDaysUntilLeaseEnd(tenant.leaseEnd);
          const isLeaseExpiringSoon = daysUntilLeaseEnd <= 60 && daysUntilLeaseEnd > 0;

          return (
            <div key={tenant.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  {tenant.avatar ? (
                    <img
                      src={tenant.avatar}
                      alt={tenant.name}
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-8 h-8 text-gray-400" />
                    </div>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-1">{tenant.name}</h3>
                      <p className="text-sm text-gray-600 flex items-center mb-1">
                        <MapPin className="w-4 h-4 mr-1" />
                        {tenant.propertyAddress}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <span className="flex items-center">
                          <Mail className="w-4 h-4 mr-1" />
                          {tenant.email}
                        </span>
                        <span className="flex items-center">
                          <Phone className="w-4 h-4 mr-1" />
                          {tenant.phone}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPaymentStatusColor(tenant.paymentStatus)}`}>
                        {getPaymentStatusIcon(tenant.paymentStatus)}
                        <span className="ml-1 capitalize">{tenant.paymentStatus}</span>
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getLeaseStatusColor(tenant.leaseStatus)}`}>
                        {tenant.leaseStatus}
                      </span>
                      {isLeaseExpiringSoon && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Expires in {daysUntilLeaseEnd} days
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div>
                      <p className="text-sm text-gray-500">Monthly Rent</p>
                      <p className="font-semibold">${tenant.monthlyRent.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Lease Period</p>
                      <p className="font-semibold">{formatDate(tenant.leaseStart)} - {formatDate(tenant.leaseEnd)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Payment History</p>
                      <p className="font-semibold">{tenant.paymentHistory.onTime}/{tenant.paymentHistory.total} on time</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Rating</p>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < tenant.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                            }`}
                          />
                        ))}
                        <span className="ml-1 text-sm font-semibold">{tenant.rating}/5</span>
                      </div>
                    </div>
                  </div>

                  {tenant.notes && (
                    <div className="bg-blue-50 rounded-lg p-3 mb-4">
                      <p className="text-sm text-blue-800">{tenant.notes}</p>
                    </div>
                  )}

                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Maintenance: {tenant.maintenanceRequests} requests</span>
                      <span>Next payment: {formatDate(tenant.nextPayment)}</span>
                    </div>
                    <div className="flex space-x-2">
                      <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <MessageSquare className="w-4 h-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors">
                        <Edit className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {sortedTenants.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No tenants found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterStatus !== 'all' || filterLease !== 'all'
              ? 'Try adjusting your search or filters.'
              : 'No tenants have been added yet.'
            }
          </p>
          <button className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
            Add First Tenant
          </button>
        </div>
      )}
    </div>
  );
}
