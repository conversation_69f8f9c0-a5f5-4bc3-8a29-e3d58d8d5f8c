'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Building2, Menu, X, User, LogOut } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface NavigationProps {
  showAuthButtons?: boolean;
  transparent?: boolean;
  className?: string;
}

export default function Navigation({ 
  showAuthButtons = true, 
  transparent = false,
  className = ""
}: NavigationProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const navLinks = [
    { href: '/marketplace', label: 'Marketplace' },
    { href: '/tokenize', label: 'Tokenize Property' },
    { href: '/about', label: 'Team' },
    { href: '/community', label: 'Community' },
    { href: '/docs', label: 'Docs' },
    { href: '/blog', label: 'Blog' },
  ];

  const externalLinks = [
    { href: 'https://managelife.io', label: 'Solutions' },
    { href: 'https://managelife.io', label: 'Official Site' },
  ];

  const baseClasses = transparent 
    ? "absolute top-0 left-0 right-0 z-50 bg-transparent" 
    : "bg-white shadow-sm border-b border-gray-200";

  return (
    <header className={`${baseClasses} ${className}`}>
      <div className="mobile-container">
        <div className="flex items-center justify-between py-3 sm:py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 touch-target">
            <img
              src="/logo/logo.png"
              alt="ManageLife"
              className="h-8 w-auto sm:h-10"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6 lg:space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`mobile-nav-item ${
                  transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                } transition-colors px-3 py-2 rounded-md text-sm font-medium`}
              >
                {link.label}
              </Link>
            ))}
            {externalLinks.map((link) => (
              <a
                key={link.href}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                className={`mobile-nav-item ${
                  transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                } transition-colors px-3 py-2 rounded-md text-sm font-medium`}
              >
                {link.label}
              </a>
            ))}
          </nav>

          {/* Auth Buttons / User Menu */}
          <div className="flex items-center space-x-2 sm:space-x-4">
            {showAuthButtons && (
              <>
                {user ? (
                  <div className="hidden sm:flex items-center space-x-3">
                    <Link
                      href="/dashboard"
                      className={`flex items-center space-x-2 touch-target px-3 py-2 rounded-md ${
                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                      } transition-colors`}
                    >
                      <User className="w-4 h-4" />
                      <span className="hidden lg:inline">Dashboard</span>
                    </Link>
                    <button
                      onClick={handleLogout}
                      className={`flex items-center space-x-2 touch-target px-3 py-2 rounded-md ${
                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                      } transition-colors`}
                    >
                      <LogOut className="w-4 h-4" />
                      <span className="hidden lg:inline">Logout</span>
                    </button>
                  </div>
                ) : (
                  <div className="hidden sm:flex items-center space-x-3">
                    <Link
                      href="/auth/login"
                      className={`touch-target px-3 py-2 rounded-md ${
                        transparent ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-blue-600'
                      } transition-colors text-sm font-medium`}
                    >
                      Sign In
                    </Link>
                    <Link
                      href="/auth/register"
                      className="mobile-btn bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:shadow-lg transition-all duration-300 web3-glow text-sm font-semibold"
                    >
                      Get Started
                    </Link>
                  </div>
                )}
              </>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className={`md:hidden touch-target p-2 rounded-md ${
                transparent ? 'text-white hover:bg-white/10' : 'text-gray-600 hover:bg-gray-100'
              } transition-colors`}
              aria-label={mobileMenuOpen ? 'Close menu' : 'Open menu'}
            >
              {mobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className={`md:hidden py-4 border-t ${
            transparent ? 'border-white/20 bg-black/20 backdrop-blur-sm' : 'border-gray-200 bg-white'
          }`}>
            <div className="mobile-space-y-4">
              {/* Navigation Links */}
              <div className="space-y-1">
                {navLinks.map((link) => (
                  <Link
                    key={link.href}
                    href={link.href}
                    className={`mobile-nav-item block w-full ${
                      transparent ? 'text-white hover:text-blue-200 hover:bg-white/10' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                    } transition-colors`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {link.label}
                  </Link>
                ))}
                {externalLinks.map((link) => (
                  <a
                    key={link.href}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`mobile-nav-item block w-full ${
                      transparent ? 'text-white hover:text-blue-200 hover:bg-white/10' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                    } transition-colors`}
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {link.label}
                  </a>
                ))}
              </div>

              {/* Auth Section */}
              {showAuthButtons && (
                <div className={`pt-4 border-t space-y-3 ${
                  transparent ? 'border-white/20' : 'border-gray-200'
                }`}>
                  {user ? (
                    <>
                      <Link
                        href="/dashboard"
                        className={`mobile-nav-item flex items-center space-x-3 w-full ${
                          transparent ? 'text-white hover:text-blue-200 hover:bg-white/10' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                        } transition-colors`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        <User className="w-5 h-5" />
                        <span>Dashboard</span>
                      </Link>
                      <button
                        onClick={() => {
                          handleLogout();
                          setMobileMenuOpen(false);
                        }}
                        className={`mobile-nav-item flex items-center space-x-3 w-full text-left ${
                          transparent ? 'text-white hover:text-blue-200 hover:bg-white/10' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                        } transition-colors`}
                      >
                        <LogOut className="w-5 h-5" />
                        <span>Logout</span>
                      </button>
                    </>
                  ) : (
                    <>
                      <Link
                        href="/auth/login"
                        className={`mobile-nav-item block w-full ${
                          transparent ? 'text-white hover:text-blue-200 hover:bg-white/10' : 'text-gray-700 hover:text-blue-600 hover:bg-blue-50'
                        } transition-colors`}
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Sign In
                      </Link>
                      <Link
                        href="/auth/register"
                        className="mobile-btn bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg text-center hover:shadow-lg transition-all duration-300 web3-glow font-semibold"
                        onClick={() => setMobileMenuOpen(false)}
                      >
                        Get Started
                      </Link>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
