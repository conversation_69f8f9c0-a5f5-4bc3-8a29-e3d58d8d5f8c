'use client';

import { useState } from 'react';
import Link from 'next/link';
import { 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Heart, 
  Coins, 
  TrendingUp,
  Calendar,
  Star,
  Eye
} from 'lucide-react';
import { Property } from '@/types';
import { formatCurrency, formatDate, calculateAnnualYield } from '@/utils';

interface PropertyCardProps {
  property: Property & {
    monthlyRent?: number;
    owner?: {
      name: string;
      rating: number;
    };
    views?: number;
    listedDate?: Date;
  };
  onFavoriteToggle?: (propertyId: string) => void;
  isFavorite?: boolean;
  showOwnerInfo?: boolean;
  showStats?: boolean;
}

export default function PropertyCard({ 
  property, 
  onFavoriteToggle, 
  isFavorite = false,
  showOwnerInfo = false,
  showStats = false
}: PropertyCardProps) {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onFavoriteToggle) {
      onFavoriteToggle(property.id);
    }
  };

  const annualYield = property.monthlyRent && property.price 
    ? calculateAnnualYield(property.monthlyRent, property.price)
    : null;

  return (
    <Link href={`/marketplace/${property.id}`}>
      <div className="mobile-card card-hover group">
        {/* Property Image */}
        <div className="relative h-40 sm:h-48 bg-gray-200 overflow-hidden">
          {!imageError ? (
            <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
              <div className="text-gray-400 text-center">
                <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-2 bg-gray-300 rounded-lg flex items-center justify-center">
                  <Square className="w-6 h-6 sm:w-8 sm:h-8" />
                </div>
                <p className="text-xs sm:text-sm">Property Image</p>
              </div>
            </div>
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <span className="text-gray-400 text-xs sm:text-sm">Image not available</span>
            </div>
          )}

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

          {/* Badges */}
          <div className="absolute top-2 sm:top-4 left-2 sm:left-4 flex flex-col space-y-1 sm:space-y-2">
            {property.featured && (
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold">
                Featured
              </div>
            )}
            <div className={`px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold ${
              property.status === 'available'
                ? 'bg-green-600 text-white'
                : 'bg-yellow-600 text-white'
            }`}>
              {property.status === 'available' ? 'Available' : 'Pending'}
            </div>
          </div>

          {/* NFT Badge */}
          {property.isTokenized && (
            <div className="absolute top-2 sm:top-4 right-2 sm:right-4 bg-green-600 text-white px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-semibold flex items-center">
              <Coins className="w-3 h-3 mr-1" />
              NFT
            </div>
          )}

          {/* Favorite Button */}
          <button
            onClick={handleFavoriteClick}
            className="touch-target absolute bottom-2 sm:bottom-4 right-2 sm:right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-colors group-hover:scale-110 transform duration-200"
          >
            <Heart className={`w-4 h-4 sm:w-5 sm:h-5 ${isFavorite ? 'text-red-500 fill-current' : 'text-gray-600'}`} />
          </button>

          {/* Stats Overlay */}
          {showStats && property.views && (
            <div className="absolute bottom-2 sm:bottom-4 left-2 sm:left-4 bg-black/50 text-white px-2 py-1 rounded text-xs sm:text-sm flex items-center">
              <Eye className="w-3 h-3 mr-1" />
              {property.views}
            </div>
          )}
        </div>

        {/* Property Details */}
        <div className="p-4 sm:p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-2">
            <h3 className="text-base sm:text-lg font-semibold text-gray-900 line-clamp-1 group-hover:text-blue-600 transition-colors flex-1 mr-2">
              {property.title}
            </h3>
            <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
              property.listingType === 'sale'
                ? 'bg-blue-100 text-blue-600'
                : 'bg-green-100 text-green-600'
            }`}>
              {property.listingType === 'sale' ? 'Sale' : 'Rent'}
            </span>
          </div>

          {/* Location */}
          <div className="flex items-center text-gray-600 mb-3">
            <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
            <span className="text-sm line-clamp-1 mobile-text-responsive">{property.city}, {property.state}</span>
          </div>

          {/* Description */}
          <p className="text-gray-600 text-sm mb-3 sm:mb-4 line-clamp-2 mobile-text-responsive">
            {property.description}
          </p>

          {/* Property Features */}
          <div className="flex items-center space-x-3 sm:space-x-4 mb-3 sm:mb-4 text-sm text-gray-600">
            {property.bedrooms > 0 && (
              <div className="flex items-center">
                <Bed className="w-4 h-4 mr-1" />
                <span className="mobile-text-responsive">{property.bedrooms}</span>
              </div>
            )}
            <div className="flex items-center">
              <Bath className="w-4 h-4 mr-1" />
              <span className="mobile-text-responsive">{property.bathrooms}</span>
            </div>
            <div className="flex items-center">
              <Square className="w-4 h-4 mr-1" />
              <span className="mobile-text-responsive">{property.squareFeet?.toLocaleString()} sqft</span>
            </div>
          </div>

          {/* Owner Info */}
          {showOwnerInfo && property.owner && (
            <div className="flex items-center space-x-2 mb-3 sm:mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex-shrink-0"></div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">{property.owner.name}</p>
                <div className="flex items-center text-xs text-gray-600">
                  <Star className="w-3 h-3 text-yellow-400 mr-1" />
                  <span>{property.owner.rating}</span>
                </div>
              </div>
            </div>
          )}

          {/* Price and Metrics */}
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <p className="text-lg sm:text-2xl font-bold text-gray-900">
                {formatCurrency(property.price, property.currency)}
                {property.listingType === 'rent' && <span className="text-sm font-normal text-gray-600">/month</span>}
              </p>

              {/* Additional Info */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 mt-1 space-y-1 sm:space-y-0">
                {property.isTokenized && (
                  <span className="text-xs text-green-600 font-medium">
                    Token #{property.nftTokenId}
                  </span>
                )}
                {property.listedDate && (
                  <span className="text-xs text-gray-500">
                    Listed {formatDate(property.listedDate)}
                  </span>
                )}
              </div>
            </div>

            {/* Yield Badge */}
            {annualYield && property.listingType === 'sale' && (
              <div className="text-right flex-shrink-0 ml-2">
                <div className="flex items-center text-green-600 text-sm font-semibold">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  {annualYield.toFixed(1)}%
                </div>
                <p className="text-xs text-gray-500">Annual Yield</p>
              </div>
            )}
          </div>

          {/* Monthly Rent for Sale Properties */}
          {property.listingType === 'sale' && property.monthlyRent && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="text-sm text-gray-600">
                Rental Income: <span className="font-semibold text-gray-900">
                  {formatCurrency(property.monthlyRent, property.currency)}/month
                </span>
              </p>
            </div>
          )}

          {/* Amenities Preview */}
          {property.amenities && property.amenities.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex flex-wrap gap-1">
                {property.amenities.slice(0, 3).map((amenity, index) => (
                  <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {amenity}
                  </span>
                ))}
                {property.amenities.length > 3 && (
                  <span className="text-xs text-gray-500 px-2 py-1">
                    +{property.amenities.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}
