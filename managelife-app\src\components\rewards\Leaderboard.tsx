'use client';

import { Trophy, Medal, Award, Crown, TrendingUp } from 'lucide-react';
import { useLeaderboard } from '@/hooks/useRewards';
import { formatCurrency } from '@/utils';

export default function Leaderboard() {
  const { leaderboard, loading, error } = useLeaderboard();

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />;
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />;
      default:
        return <span className="w-6 h-6 flex items-center justify-center text-sm font-bold text-gray-600">#{rank}</span>;
    }
  };

  const getRankBadge = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white';
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white';
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-6">
          <Trophy className="w-6 h-6 mr-3 text-yellow-600" />
          <h3 className="text-lg font-semibold text-gray-900">Leaderboard</h3>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg animate-pulse">
              <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-300 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-1/4"></div>
              </div>
              <div className="h-4 bg-gray-300 rounded w-20"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center mb-6">
          <Trophy className="w-6 h-6 mr-3 text-yellow-600" />
          <h3 className="text-lg font-semibold text-gray-900">Leaderboard</h3>
        </div>
        <div className="text-center py-8">
          <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Failed to load leaderboard</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mobile-card p-4 sm:p-6">
      <div className="flex items-center mb-4 sm:mb-6">
        <Trophy className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-yellow-600" />
        <h3 className="text-base sm:text-lg font-semibold text-gray-900">Top Earners</h3>
      </div>

      {leaderboard.length === 0 ? (
        <div className="text-center py-6 sm:py-8">
          <Trophy className="w-12 h-12 sm:w-16 sm:h-16 text-gray-400 mx-auto mb-4" />
          <h4 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">No Data Yet</h4>
          <p className="text-gray-600 mobile-text-responsive">Be the first to earn rewards!</p>
        </div>
      ) : (
        <div className="space-y-2 sm:space-y-3">
          {leaderboard.map((entry, index) => {
            const rank = index + 1;
            return (
              <div
                key={entry.userId}
                className={`mobile-card p-3 sm:p-4 transition-all duration-200 ${
                  rank <= 3
                    ? 'bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200'
                    : 'bg-gray-50 hover:bg-gray-100'
                }`}
              >
                {/* Mobile Layout */}
                <div className="flex items-start space-x-3 sm:hidden">
                  {/* Rank */}
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${getRankBadge(rank)}`}>
                    {rank <= 3 ? getRankIcon(rank) : <span className="font-bold text-xs">#{rank}</span>}
                  </div>

                  {/* User Info and Stats */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                        <span className="text-white font-semibold text-xs">
                          {entry.user?.name ? entry.user.name.split(' ').map(n => n[0]).join('') : 'U'}
                        </span>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 text-sm truncate">
                          {entry.user?.name || 'Anonymous User'}
                        </h4>
                        <p className="text-xs text-gray-600">
                          {entry.achievements?.length || 0} achievements
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-bold text-sm text-blue-600">
                          {formatCurrency(entry.totalClaimed, 'MLIFE')}
                        </p>
                        <p className="text-xs text-gray-600">
                          {entry.streakDays} day streak
                        </p>
                      </div>

                      {/* Champion badge for mobile */}
                      {rank <= 3 && (
                        <div className="flex-shrink-0">
                          {rank === 1 && (
                            <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium">
                              Champion
                            </span>
                          )}
                          {rank === 2 && (
                            <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-medium">
                              Runner-up
                            </span>
                          )}
                          {rank === 3 && (
                            <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full font-medium">
                              3rd Place
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Desktop Layout */}
                <div className="hidden sm:flex items-center space-x-4">
                  {/* Rank */}
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getRankBadge(rank)}`}>
                    {rank <= 3 ? getRankIcon(rank) : <span className="font-bold">#{rank}</span>}
                  </div>

                  {/* User Info */}
                  <div className="flex items-center space-x-3 flex-1">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-semibold text-sm">
                        {entry.user?.name ? entry.user.name.split(' ').map(n => n[0]).join('') : 'U'}
                      </span>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">
                        {entry.user?.name || 'Anonymous User'}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {entry.achievements?.length || 0} achievements
                      </p>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="text-right">
                    <p className="font-bold text-lg text-blue-600">
                      {formatCurrency(entry.totalClaimed, 'MLIFE')}
                    </p>
                    <p className="text-sm text-gray-600">
                      {entry.streakDays} day streak
                    </p>
                  </div>

                  {/* Special badges for top 3 */}
                  {rank <= 3 && (
                    <div className="flex flex-col items-center">
                      {rank === 1 && (
                        <span className="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full font-medium">
                          Champion
                        </span>
                      )}
                      {rank === 2 && (
                        <span className="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded-full font-medium">
                          Runner-up
                        </span>
                      )}
                      {rank === 3 && (
                        <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full font-medium">
                          Third Place
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Footer */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <p className="text-center text-sm text-gray-600">
          Earn more $MLIFE tokens by completing activities and climb the leaderboard!
        </p>
      </div>
    </div>
  );
}
